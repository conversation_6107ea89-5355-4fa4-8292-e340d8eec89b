"""
Visualization Dashboard for Airline No-Show Prediction System
Creates comprehensive charts and interactive dashboards
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import logging
from pathlib import Path

class Visualizer:
    """
    Comprehensive visualization system for airline booking data analysis
    """
    
    def __init__(self, config):
        """
        Initialize Visualizer with configuration
        
        Args:
            config (dict): Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.output_dir = Path("visualizations")
        self.output_dir.mkdir(exist_ok=True)
        
        # Set style
        plt.style.use('seaborn-v0_8')
        sns.set_palette(self.config['visualization']['color_palette'])
        
    def create_dashboard(self, processed_data, eda_results):
        """
        Create comprehensive visualization dashboard
        
        Args:
            processed_data (dict): Processed data from DataProcessor
            eda_results (dict): Results from EDA analysis
        """
        self.logger.info("Creating visualization dashboard...")
        
        data = processed_data['processed_data']
        
        # Create individual visualizations
        self._create_no_show_overview(data, eda_results)
        self._create_temporal_analysis(data, eda_results)
        self._create_correlation_heatmap(eda_results)
        self._create_route_analysis(data, eda_results)
        self._create_price_analysis(data, eda_results)
        self._create_passenger_behavior_charts(data, eda_results)
        
        # Create interactive dashboard
        self._create_interactive_dashboard(data, eda_results)
        
        self.logger.info("Visualization dashboard created successfully")
    
    def _create_no_show_overview(self, data, eda_results):
        """Create overview charts for no-show analysis"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('No-Show Analysis Overview', fontsize=16, fontweight='bold')
        
        # Overall no-show rate
        no_show_counts = data['no_show'].value_counts()
        axes[0, 0].pie(no_show_counts.values, labels=['Show', 'No-Show'], autopct='%1.1f%%',
                       colors=['lightgreen', 'lightcoral'])
        axes[0, 0].set_title('Overall No-Show Rate')
        
        # No-show by passenger type
        if 'passenger_type' in data.columns:
            passenger_no_show = data.groupby('passenger_type')['no_show'].mean()
            axes[0, 1].bar(passenger_no_show.index, passenger_no_show.values)
            axes[0, 1].set_title('No-Show Rate by Passenger Type')
            axes[0, 1].set_ylabel('No-Show Rate')
            axes[0, 1].tick_params(axis='x', rotation=45)
        
        # No-show by booking lead time
        if 'booking_lead_time' in data.columns:
            lead_time_bins = pd.cut(data['booking_lead_time'], bins=10)
            lead_time_no_show = data.groupby(lead_time_bins)['no_show'].mean()
            axes[1, 0].plot(range(len(lead_time_no_show)), lead_time_no_show.values, marker='o')
            axes[1, 0].set_title('No-Show Rate by Booking Lead Time')
            axes[1, 0].set_ylabel('No-Show Rate')
            axes[1, 0].set_xlabel('Lead Time (binned)')
        
        # No-show distribution by price
        if 'ticket_price' in data.columns:
            no_show_prices = data[data['no_show'] == 1]['ticket_price']
            show_prices = data[data['no_show'] == 0]['ticket_price']
            axes[1, 1].hist([show_prices, no_show_prices], bins=30, alpha=0.7, 
                           label=['Show', 'No-Show'], color=['lightgreen', 'lightcoral'])
            axes[1, 1].set_title('Price Distribution: Show vs No-Show')
            axes[1, 1].set_xlabel('Ticket Price')
            axes[1, 1].set_ylabel('Frequency')
            axes[1, 1].legend()
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'no_show_overview.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_temporal_analysis(self, data, eda_results):
        """Create temporal pattern visualizations"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Temporal Pattern Analysis', fontsize=16, fontweight='bold')
        
        # Monthly booking patterns
        if 'booking_month' in data.columns:
            monthly_data = data.groupby('booking_month').agg({
                'no_show': ['count', 'mean']
            })
            
            ax1 = axes[0, 0]
            ax2 = ax1.twinx()
            
            bars = ax1.bar(monthly_data.index, monthly_data[('no_show', 'count')], 
                          alpha=0.7, color='skyblue', label='Total Bookings')
            line = ax2.plot(monthly_data.index, monthly_data[('no_show', 'mean')], 
                           color='red', marker='o', linewidth=2, label='No-Show Rate')
            
            ax1.set_xlabel('Month')
            ax1.set_ylabel('Total Bookings', color='skyblue')
            ax2.set_ylabel('No-Show Rate', color='red')
            ax1.set_title('Monthly Booking and No-Show Patterns')
        
        # Day of week patterns
        if 'booking_dow' in data.columns:
            dow_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
            dow_data = data.groupby('booking_dow')['no_show'].mean().reindex(dow_order)
            axes[0, 1].bar(range(len(dow_data)), dow_data.values)
            axes[0, 1].set_title('No-Show Rate by Day of Week (Booking)')
            axes[0, 1].set_xticks(range(len(dow_data)))
            axes[0, 1].set_xticklabels(dow_data.index, rotation=45)
            axes[0, 1].set_ylabel('No-Show Rate')
        
        # Lead time analysis
        if 'booking_lead_time' in data.columns:
            lead_time_categories = pd.cut(data['booking_lead_time'], 
                                        bins=[0, 7, 30, 90, float('inf')],
                                        labels=['0-7 days', '8-30 days', '31-90 days', '90+ days'])
            lead_time_no_show = data.groupby(lead_time_categories)['no_show'].mean()
            axes[1, 0].bar(range(len(lead_time_no_show)), lead_time_no_show.values)
            axes[1, 0].set_title('No-Show Rate by Booking Lead Time')
            axes[1, 0].set_xticks(range(len(lead_time_no_show)))
            axes[1, 0].set_xticklabels(lead_time_no_show.index, rotation=45)
            axes[1, 0].set_ylabel('No-Show Rate')
        
        # Weekend vs weekday
        if 'is_weekend_flight' in data.columns:
            weekend_data = data.groupby('is_weekend_flight')['no_show'].mean()
            axes[1, 1].bar(['Weekday', 'Weekend'], weekend_data.values, 
                          color=['lightblue', 'orange'])
            axes[1, 1].set_title('No-Show Rate: Weekday vs Weekend Flights')
            axes[1, 1].set_ylabel('No-Show Rate')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'temporal_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_correlation_heatmap(self, eda_results):
        """Create correlation heatmap"""
        if 'correlations' not in eda_results:
            return
        
        correlation_matrix = eda_results['correlations']['correlation_matrix']
        
        plt.figure(figsize=(12, 10))
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
        sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='coolwarm', 
                   center=0, square=True, linewidths=0.5)
        plt.title('Feature Correlation Matrix', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig(self.output_dir / 'correlation_heatmap.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_route_analysis(self, data, eda_results):
        """Create route-specific analysis charts"""
        if 'route' not in data.columns:
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Route Analysis', fontsize=16, fontweight='bold')
        
        # Top routes by volume
        top_routes = data['route'].value_counts().head(10)
        axes[0, 0].barh(range(len(top_routes)), top_routes.values)
        axes[0, 0].set_yticks(range(len(top_routes)))
        axes[0, 0].set_yticklabels(top_routes.index)
        axes[0, 0].set_title('Top 10 Routes by Booking Volume')
        axes[0, 0].set_xlabel('Number of Bookings')
        
        # Routes with highest no-show rates
        route_no_show = data.groupby('route')['no_show'].mean().sort_values(ascending=False).head(10)
        axes[0, 1].barh(range(len(route_no_show)), route_no_show.values)
        axes[0, 1].set_yticks(range(len(route_no_show)))
        axes[0, 1].set_yticklabels(route_no_show.index)
        axes[0, 1].set_title('Top 10 Routes by No-Show Rate')
        axes[0, 1].set_xlabel('No-Show Rate')
        
        # Route popularity vs no-show rate scatter
        if 'route_popularity' in data.columns and 'route_no_show_rate' in data.columns:
            route_summary = data.groupby('route').agg({
                'route_popularity': 'first',
                'route_no_show_rate': 'first'
            })
            axes[1, 0].scatter(route_summary['route_popularity'], 
                              route_summary['route_no_show_rate'], alpha=0.6)
            axes[1, 0].set_xlabel('Route Popularity (Total Bookings)')
            axes[1, 0].set_ylabel('Route No-Show Rate')
            axes[1, 0].set_title('Route Popularity vs No-Show Rate')
        
        # Price distribution by top routes
        if 'ticket_price' in data.columns:
            top_5_routes = data['route'].value_counts().head(5).index
            route_price_data = [data[data['route'] == route]['ticket_price'] for route in top_5_routes]
            axes[1, 1].boxplot(route_price_data, labels=top_5_routes)
            axes[1, 1].set_title('Price Distribution for Top 5 Routes')
            axes[1, 1].set_ylabel('Ticket Price')
            axes[1, 1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'route_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_price_analysis(self, data, eda_results):
        """Create price-related visualizations"""
        if 'ticket_price' not in data.columns:
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Price Analysis', fontsize=16, fontweight='bold')
        
        # Price distribution
        axes[0, 0].hist(data['ticket_price'], bins=50, alpha=0.7, color='skyblue')
        axes[0, 0].set_title('Ticket Price Distribution')
        axes[0, 0].set_xlabel('Price')
        axes[0, 0].set_ylabel('Frequency')
        
        # Price vs no-show rate
        price_bins = pd.cut(data['ticket_price'], bins=10)
        price_no_show = data.groupby(price_bins)['no_show'].mean()
        axes[0, 1].plot(range(len(price_no_show)), price_no_show.values, marker='o')
        axes[0, 1].set_title('No-Show Rate by Price Range')
        axes[0, 1].set_xlabel('Price Range (binned)')
        axes[0, 1].set_ylabel('No-Show Rate')
        
        # Price by passenger type
        if 'passenger_type' in data.columns:
            passenger_types = data['passenger_type'].unique()
            price_by_type = [data[data['passenger_type'] == pt]['ticket_price'] for pt in passenger_types]
            axes[1, 0].boxplot(price_by_type, labels=passenger_types)
            axes[1, 0].set_title('Price Distribution by Passenger Type')
            axes[1, 0].set_ylabel('Ticket Price')
            axes[1, 0].tick_params(axis='x', rotation=45)
        
        # Price quartile analysis
        price_quartiles = pd.qcut(data['ticket_price'], q=4, labels=['Q1', 'Q2', 'Q3', 'Q4'])
        quartile_no_show = data.groupby(price_quartiles)['no_show'].mean()
        axes[1, 1].bar(quartile_no_show.index, quartile_no_show.values)
        axes[1, 1].set_title('No-Show Rate by Price Quartile')
        axes[1, 1].set_ylabel('No-Show Rate')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'price_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_passenger_behavior_charts(self, data, eda_results):
        """Create passenger behavior analysis charts"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Passenger Behavior Analysis', fontsize=16, fontweight='bold')
        
        # Previous no-shows impact
        if 'previous_no_shows' in data.columns:
            prev_no_show_impact = data.groupby('previous_no_shows')['no_show'].mean()
            axes[0, 0].bar(prev_no_show_impact.index, prev_no_show_impact.values)
            axes[0, 0].set_title('Current No-Show Rate by Previous No-Shows')
            axes[0, 0].set_xlabel('Previous No-Shows')
            axes[0, 0].set_ylabel('Current No-Show Rate')
        
        # Risk score distribution
        if 'passenger_risk_score' in data.columns:
            axes[0, 1].hist(data['passenger_risk_score'], bins=30, alpha=0.7, color='orange')
            axes[0, 1].set_title('Passenger Risk Score Distribution')
            axes[0, 1].set_xlabel('Risk Score')
            axes[0, 1].set_ylabel('Frequency')
        
        # Booking channel analysis
        if 'booking_channel' in data.columns:
            channel_no_show = data.groupby('booking_channel')['no_show'].mean()
            axes[1, 0].bar(channel_no_show.index, channel_no_show.values)
            axes[1, 0].set_title('No-Show Rate by Booking Channel')
            axes[1, 0].set_ylabel('No-Show Rate')
            axes[1, 0].tick_params(axis='x', rotation=45)
        
        # Lead time vs risk score
        if 'booking_lead_time' in data.columns and 'passenger_risk_score' in data.columns:
            axes[1, 1].scatter(data['booking_lead_time'], data['passenger_risk_score'], 
                              c=data['no_show'], cmap='RdYlBu', alpha=0.6)
            axes[1, 1].set_xlabel('Booking Lead Time')
            axes[1, 1].set_ylabel('Passenger Risk Score')
            axes[1, 1].set_title('Lead Time vs Risk Score (colored by no-show)')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'passenger_behavior.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_interactive_dashboard(self, data, eda_results):
        """Create interactive Plotly dashboard"""
        self.logger.info("Creating interactive dashboard...")
        
        # Create subplots
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('No-Show Rate by Route', 'Temporal Patterns', 
                          'Price vs No-Show', 'Feature Importance'),
            specs=[[{"secondary_y": False}, {"secondary_y": True}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )
        
        # Route analysis
        if 'route' in data.columns:
            route_data = data.groupby('route')['no_show'].agg(['count', 'mean']).reset_index()
            route_data = route_data.sort_values('mean', ascending=False).head(10)
            
            fig.add_trace(
                go.Bar(x=route_data['route'], y=route_data['mean'], 
                      name='No-Show Rate', showlegend=False),
                row=1, col=1
            )
        
        # Temporal patterns
        if 'booking_month' in data.columns:
            monthly_data = data.groupby('booking_month').agg({
                'no_show': ['count', 'mean']
            }).reset_index()
            
            fig.add_trace(
                go.Bar(x=monthly_data['booking_month'], y=monthly_data[('no_show', 'count')],
                      name='Bookings', showlegend=False),
                row=1, col=2
            )
            
            fig.add_trace(
                go.Scatter(x=monthly_data['booking_month'], y=monthly_data[('no_show', 'mean')],
                          mode='lines+markers', name='No-Show Rate', 
                          yaxis='y2', showlegend=False),
                row=1, col=2, secondary_y=True
            )
        
        # Price analysis
        if 'ticket_price' in data.columns:
            fig.add_trace(
                go.Scatter(x=data['ticket_price'], y=data['no_show'],
                          mode='markers', opacity=0.6, name='Price vs No-Show',
                          showlegend=False),
                row=2, col=1
            )
        
        # Update layout
        fig.update_layout(
            title_text="Airline No-Show Analysis Dashboard",
            title_x=0.5,
            height=800,
            showlegend=False
        )
        
        # Save interactive dashboard
        fig.write_html(self.output_dir / 'interactive_dashboard.html')
        
        self.logger.info("Interactive dashboard saved as HTML")
