"""
Exploratory Data Analysis for Airline No-Show Prediction System
Performs comprehensive statistical analysis and pattern discovery
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import chi2_contingency
import logging
from pathlib import Path

class EDAAnalyzer:
    """
    Comprehensive exploratory data analysis for airline booking data
    """
    
    def __init__(self, config):
        """
        Initialize EDA Analyzer with configuration
        
        Args:
            config (dict): Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.results = {}
        
    def analyze(self, processed_data):
        """
        Perform comprehensive EDA analysis
        
        Args:
            processed_data (dict): Processed data from DataProcessor
            
        Returns:
            dict: Analysis results and insights
        """
        self.logger.info("Starting exploratory data analysis")
        
        data = processed_data['processed_data']
        
        # Basic statistics
        self.results['basic_stats'] = self._basic_statistics(data)
        
        # No-show analysis
        self.results['no_show_analysis'] = self._analyze_no_shows(data)
        
        # Temporal patterns
        self.results['temporal_patterns'] = self._analyze_temporal_patterns(data)
        
        # Correlation analysis
        self.results['correlations'] = self._correlation_analysis(data)
        
        # Route analysis
        self.results['route_analysis'] = self._analyze_routes(data)
        
        # Price analysis
        self.results['price_analysis'] = self._analyze_pricing(data)
        
        # Passenger behavior
        self.results['passenger_behavior'] = self._analyze_passenger_behavior(data)
        
        # Statistical tests
        self.results['statistical_tests'] = self._statistical_tests(data)
        
        self.logger.info("EDA analysis completed successfully")
        return self.results
    
    def _basic_statistics(self, data):
        """Generate basic statistical summary"""
        self.logger.info("Generating basic statistics...")
        
        stats = {
            'total_bookings': len(data),
            'total_no_shows': data['no_show'].sum(),
            'no_show_rate': data['no_show'].mean(),
            'unique_routes': data['route'].nunique() if 'route' in data.columns else 0,
            'date_range': {
                'start': data['booking_date'].min() if 'booking_date' in data.columns else None,
                'end': data['booking_date'].max() if 'booking_date' in data.columns else None
            },
            'price_stats': data['ticket_price'].describe().to_dict() if 'ticket_price' in data.columns else {}
        }
        
        return stats
    
    def _analyze_no_shows(self, data):
        """Analyze no-show patterns"""
        self.logger.info("Analyzing no-show patterns...")
        
        analysis = {}
        
        # Overall no-show rate
        analysis['overall_rate'] = data['no_show'].mean()
        
        # No-show by passenger type
        if 'passenger_type' in data.columns:
            analysis['by_passenger_type'] = data.groupby('passenger_type')['no_show'].agg([
                'count', 'sum', 'mean'
            ]).round(4)
        
        # No-show by booking channel
        if 'booking_channel' in data.columns:
            analysis['by_booking_channel'] = data.groupby('booking_channel')['no_show'].agg([
                'count', 'sum', 'mean'
            ]).round(4)
        
        # No-show by lead time categories
        if 'booking_lead_time' in data.columns:
            data['lead_time_category'] = pd.cut(data['booking_lead_time'], 
                                              bins=[0, 7, 30, 90, float('inf')],
                                              labels=['0-7 days', '8-30 days', '31-90 days', '90+ days'])
            analysis['by_lead_time'] = data.groupby('lead_time_category')['no_show'].agg([
                'count', 'sum', 'mean'
            ]).round(4)
        
        return analysis
    
    def _analyze_temporal_patterns(self, data):
        """Analyze temporal patterns in bookings and no-shows"""
        self.logger.info("Analyzing temporal patterns...")
        
        patterns = {}
        
        if 'booking_date' in data.columns:
            # Monthly patterns
            data['booking_month'] = data['booking_date'].dt.month
            patterns['monthly_bookings'] = data.groupby('booking_month').agg({
                'no_show': ['count', 'sum', 'mean']
            }).round(4)
            
            # Day of week patterns
            data['booking_dow'] = data['booking_date'].dt.day_name()
            patterns['dow_bookings'] = data.groupby('booking_dow').agg({
                'no_show': ['count', 'sum', 'mean']
            }).round(4)
        
        if 'flight_date' in data.columns:
            # Flight day patterns
            data['flight_dow'] = data['flight_date'].dt.day_name()
            patterns['flight_dow'] = data.groupby('flight_dow').agg({
                'no_show': ['count', 'sum', 'mean']
            }).round(4)
        
        # Weekend vs weekday analysis
        if 'is_weekend_booking' in data.columns:
            patterns['weekend_booking'] = data.groupby('is_weekend_booking')['no_show'].agg([
                'count', 'sum', 'mean'
            ]).round(4)
        
        if 'is_weekend_flight' in data.columns:
            patterns['weekend_flight'] = data.groupby('is_weekend_flight')['no_show'].agg([
                'count', 'sum', 'mean'
            ]).round(4)
        
        return patterns
    
    def _correlation_analysis(self, data):
        """Perform correlation analysis"""
        self.logger.info("Performing correlation analysis...")
        
        # Select numerical columns
        numerical_cols = data.select_dtypes(include=[np.number]).columns.tolist()
        
        # Calculate correlation matrix
        correlation_matrix = data[numerical_cols].corr()
        
        # Find strong correlations with no_show
        if 'no_show' in correlation_matrix.columns:
            no_show_correlations = correlation_matrix['no_show'].abs().sort_values(ascending=False)
            strong_correlations = no_show_correlations[no_show_correlations > 0.1]
        else:
            strong_correlations = pd.Series()
        
        return {
            'correlation_matrix': correlation_matrix,
            'no_show_correlations': strong_correlations,
            'top_features': strong_correlations.head(10).to_dict()
        }
    
    def _analyze_routes(self, data):
        """Analyze route-specific patterns"""
        self.logger.info("Analyzing route patterns...")
        
        if 'route' not in data.columns:
            return {}
        
        route_analysis = data.groupby('route').agg({
            'no_show': ['count', 'sum', 'mean'],
            'ticket_price': ['mean', 'std'] if 'ticket_price' in data.columns else 'count',
            'booking_lead_time': ['mean', 'std'] if 'booking_lead_time' in data.columns else 'count'
        }).round(4)
        
        # Top routes by volume
        top_routes_volume = data['route'].value_counts().head(10)
        
        # Routes with highest no-show rates
        route_no_show_rates = data.groupby('route')['no_show'].mean().sort_values(ascending=False)
        top_routes_no_show = route_no_show_rates.head(10)
        
        return {
            'route_summary': route_analysis,
            'top_routes_by_volume': top_routes_volume,
            'top_routes_by_no_show_rate': top_routes_no_show
        }
    
    def _analyze_pricing(self, data):
        """Analyze pricing patterns and impact on no-shows"""
        self.logger.info("Analyzing pricing patterns...")
        
        if 'ticket_price' not in data.columns:
            return {}
        
        # Price distribution
        price_stats = data['ticket_price'].describe()
        
        # Price vs no-show analysis
        if 'price_category' in data.columns:
            price_no_show = data.groupby('price_category')['no_show'].agg([
                'count', 'sum', 'mean'
            ]).round(4)
        else:
            price_no_show = pd.DataFrame()
        
        # Price elasticity analysis
        price_quartiles = pd.qcut(data['ticket_price'], q=4, labels=['Q1', 'Q2', 'Q3', 'Q4'])
        quartile_analysis = data.groupby(price_quartiles)['no_show'].agg([
            'count', 'sum', 'mean'
        ]).round(4)
        
        return {
            'price_distribution': price_stats,
            'price_category_analysis': price_no_show,
            'quartile_analysis': quartile_analysis
        }
    
    def _analyze_passenger_behavior(self, data):
        """Analyze passenger behavior patterns"""
        self.logger.info("Analyzing passenger behavior...")
        
        behavior = {}
        
        # Previous no-show impact
        if 'previous_no_shows' in data.columns:
            behavior['previous_no_shows_impact'] = data.groupby('previous_no_shows')['no_show'].agg([
                'count', 'sum', 'mean'
            ]).round(4)
        
        # Risk score analysis
        if 'passenger_risk_score' in data.columns:
            risk_quartiles = pd.qcut(data['passenger_risk_score'], q=4, 
                                   labels=['Low Risk', 'Medium-Low', 'Medium-High', 'High Risk'])
            behavior['risk_score_analysis'] = data.groupby(risk_quartiles)['no_show'].agg([
                'count', 'sum', 'mean'
            ]).round(4)
        
        return behavior
    
    def _statistical_tests(self, data):
        """Perform statistical significance tests"""
        self.logger.info("Performing statistical tests...")
        
        tests = {}
        
        # Chi-square test for categorical variables
        categorical_vars = ['passenger_type', 'booking_channel', 'route']
        
        for var in categorical_vars:
            if var in data.columns:
                try:
                    contingency_table = pd.crosstab(data[var], data['no_show'])
                    chi2, p_value, dof, expected = chi2_contingency(contingency_table)
                    tests[f'{var}_chi2'] = {
                        'chi2_statistic': chi2,
                        'p_value': p_value,
                        'degrees_of_freedom': dof,
                        'significant': p_value < 0.05
                    }
                except Exception as e:
                    self.logger.warning(f"Could not perform chi-square test for {var}: {e}")
        
        # T-test for numerical variables
        numerical_vars = ['ticket_price', 'booking_lead_time']
        
        for var in numerical_vars:
            if var in data.columns:
                try:
                    no_show_group = data[data['no_show'] == 1][var]
                    show_group = data[data['no_show'] == 0][var]
                    
                    t_stat, p_value = stats.ttest_ind(no_show_group, show_group)
                    tests[f'{var}_ttest'] = {
                        't_statistic': t_stat,
                        'p_value': p_value,
                        'significant': p_value < 0.05
                    }
                except Exception as e:
                    self.logger.warning(f"Could not perform t-test for {var}: {e}")
        
        return tests
