"""
Test cases for DataProcessor module
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
from pathlib import Path

# Add src directory to path
sys.path.append(str(Path(__file__).parent.parent / "src"))

from data_processor import DataProcessor

@pytest.fixture
def sample_config():
    """Sample configuration for testing"""
    return {
        'data': {
            'test_size': 0.2,
            'random_state': 42
        },
        'features': {
            'categorical_columns': ['passenger_type', 'route', 'booking_channel'],
            'numerical_columns': ['ticket_price', 'booking_lead_time'],
            'datetime_columns': ['booking_date', 'flight_date']
        }
    }

@pytest.fixture
def sample_data():
    """Sample booking data for testing"""
    np.random.seed(42)
    n_samples = 100
    
    booking_dates = pd.date_range('2023-01-01', '2023-12-31', periods=n_samples)
    flight_dates = booking_dates + pd.to_timedelta(np.random.randint(1, 90, n_samples), unit='D')
    
    data = pd.DataFrame({
        'booking_date': booking_dates,
        'flight_date': flight_dates,
        'route': np.random.choice(['NYC-LAX', 'CHI-MIA', 'BOS-SEA'], n_samples),
        'ticket_price': np.random.uniform(100, 1000, n_samples),
        'passenger_type': np.random.choice(['ECONOMY', 'BUSINESS', 'FIRST'], n_samples),
        'booking_channel': np.random.choice(['ONLINE', 'AGENT', 'PHONE'], n_samples),
        'no_show': np.random.binomial(1, 0.1, n_samples)
    })
    
    return data

class TestDataProcessor:
    """Test cases for DataProcessor class"""
    
    def test_initialization(self, sample_config):
        """Test DataProcessor initialization"""
        processor = DataProcessor(sample_config)
        assert processor.config == sample_config
        assert processor.scalers == {}
        assert processor.encoders == {}
    
    def test_validate_data_success(self, sample_config, sample_data):
        """Test successful data validation"""
        processor = DataProcessor(sample_config)
        validated_data = processor._validate_data(sample_data)
        assert len(validated_data) == len(sample_data)
        assert all(col in validated_data.columns for col in ['booking_date', 'flight_date', 'route', 'ticket_price', 'passenger_type', 'no_show'])
    
    def test_validate_data_missing_columns(self, sample_config):
        """Test data validation with missing columns"""
        processor = DataProcessor(sample_config)
        incomplete_data = pd.DataFrame({'booking_date': ['2023-01-01'], 'route': ['NYC-LAX']})
        
        with pytest.raises(ValueError, match="Missing required columns"):
            processor._validate_data(incomplete_data)
    
    def test_clean_data(self, sample_config, sample_data):
        """Test data cleaning functionality"""
        processor = DataProcessor(sample_config)
        
        # Add some duplicates and invalid data
        dirty_data = sample_data.copy()
        dirty_data = pd.concat([dirty_data, dirty_data.iloc[:5]])  # Add duplicates
        dirty_data.loc[0, 'ticket_price'] = -100  # Invalid price
        
        cleaned_data = processor._clean_data(dirty_data)
        
        # Check duplicates removed
        assert len(cleaned_data) < len(dirty_data)
        
        # Check invalid prices removed
        assert all(cleaned_data['ticket_price'] > 0)
    
    def test_engineer_features(self, sample_config, sample_data):
        """Test feature engineering"""
        processor = DataProcessor(sample_config)
        cleaned_data = processor._clean_data(sample_data)
        engineered_data = processor._engineer_features(cleaned_data)
        
        # Check new features created
        expected_features = [
            'booking_lead_time', 'booking_day_of_week', 'booking_month',
            'flight_day_of_week', 'flight_month', 'is_weekend_booking',
            'is_weekend_flight', 'passenger_risk_score'
        ]
        
        for feature in expected_features:
            assert feature in engineered_data.columns
    
    def test_handle_missing_values(self, sample_config, sample_data):
        """Test missing value handling"""
        processor = DataProcessor(sample_config)
        
        # Introduce missing values
        data_with_missing = sample_data.copy()
        data_with_missing.loc[0:5, 'ticket_price'] = np.nan
        data_with_missing.loc[10:15, 'passenger_type'] = np.nan
        
        cleaned_data = processor._handle_missing_values(data_with_missing)
        
        # Check no missing values remain
        assert cleaned_data.isnull().sum().sum() == 0
    
    def test_split_data(self, sample_config, sample_data):
        """Test data splitting"""
        processor = DataProcessor(sample_config)
        
        # Process data through pipeline
        cleaned_data = processor._clean_data(sample_data)
        engineered_data = processor._engineer_features(cleaned_data)
        no_missing_data = processor._handle_missing_values(engineered_data)
        
        split_data = processor._split_data(no_missing_data)
        
        # Check split structure
        assert 'X_train' in split_data
        assert 'X_test' in split_data
        assert 'y_train' in split_data
        assert 'y_test' in split_data
        assert 'feature_names' in split_data
        assert 'processed_data' in split_data
        
        # Check split proportions
        total_samples = len(no_missing_data)
        test_size = sample_config['data']['test_size']
        expected_test_size = int(total_samples * test_size)
        
        assert len(split_data['X_test']) == expected_test_size
        assert len(split_data['y_test']) == expected_test_size
    
    def test_full_process_pipeline(self, sample_config, sample_data):
        """Test complete processing pipeline"""
        processor = DataProcessor(sample_config)
        result = processor.process(sample_data)
        
        # Check all required outputs
        required_keys = ['X_train', 'X_test', 'y_train', 'y_test', 'feature_names', 'processed_data']
        for key in required_keys:
            assert key in result
        
        # Check data integrity
        assert len(result['X_train']) + len(result['X_test']) <= len(sample_data)
        assert len(result['y_train']) == len(result['X_train'])
        assert len(result['y_test']) == len(result['X_test'])
        
        # Check feature names
        assert len(result['feature_names']) == result['X_train'].shape[1]

if __name__ == "__main__":
    pytest.main([__file__])
