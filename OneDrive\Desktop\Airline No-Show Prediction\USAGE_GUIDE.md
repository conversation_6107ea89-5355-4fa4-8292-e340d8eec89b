# Airline No-Show Prediction System - Usage Guide

## Table of Contents
1. [Getting Started](#getting-started)
2. [Web Interface Guide](#web-interface-guide)
3. [Command Line Guide](#command-line-guide)
4. [Data Preparation](#data-preparation)
5. [Understanding Results](#understanding-results)
6. [Business Implementation](#business-implementation)
7. [Troubleshooting](#troubleshooting)

## Getting Started

### Prerequisites
- Python 3.8 or higher
- At least 4GB RAM (8GB recommended for large datasets)
- 2GB free disk space

### Installation Steps
1. **Download the system**
   ```bash
   # If you have the files, navigate to the directory
   cd "Airline No-Show Prediction"
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Verify installation**
   ```bash
   python run_tests.py
   ```

## Web Interface Guide

### Starting the Web Interface
```bash
streamlit run app.py
```
This will open your browser to `http://localhost:8501`

### Using the Interface

#### Step 1: Data Upload
- **Option A**: Upload your CSV file using the file uploader
- **Option B**: Click "Use Sample Data" to try the system with demo data

#### Step 2: Data Validation
- The system automatically validates your CSV format
- Required columns will be checked
- Data quality issues will be highlighted

#### Step 3: Run Analysis
- Click "🚀 Run Analysis" to start the complete pipeline
- Progress bar shows current processing stage
- Analysis typically takes 2-5 minutes depending on data size

#### Step 4: Review Results
Navigate through the tabs:
- **Executive Summary**: Key metrics and financial impact
- **Model Performance**: ML model comparison and accuracy
- **Insights & Recommendations**: Business insights and action items
- **Implementation Plan**: Step-by-step deployment guide

#### Step 5: Download Report
- Click "📄 Download Executive Summary" 
- Saves comprehensive HTML report for stakeholders

## Command Line Guide

### Basic Usage
```bash
# Analyze your data
python main.py --data your_booking_data.csv
```

### Advanced Options
```bash
# Custom configuration
python main.py --data data.csv --config custom_config.yaml

# Specify output directory
python main.py --data data.csv --output my_reports

# Training mode only (no predictions)
python main.py --data data.csv --mode train

# Prediction mode (requires trained models)
python main.py --data new_data.csv --mode predict
```

### Generating Sample Data
```bash
# Generate 10,000 sample records
python sample_data_generator.py --samples 10000

# Generate larger dataset
python sample_data_generator.py --samples 50000 --output large_dataset.csv
```

## Data Preparation

### Required CSV Format
Your CSV file must contain these columns:

| Column | Type | Description | Example |
|--------|------|-------------|---------|
| booking_date | Date | When booking was made | 2023-06-15 |
| flight_date | Date | Flight departure date | 2023-07-20 |
| route | String | Origin-destination | NYC-LAX |
| ticket_price | Number | Ticket price in USD | 299.99 |
| passenger_type | String | Class of service | ECONOMY |
| no_show | Integer | 1 if no-show, 0 if showed | 1 |

### Optional Columns
- `booking_channel`: ONLINE, AGENT, PHONE, MOBILE_APP
- `previous_no_shows`: Number of historical no-shows
- `meal_preference`: STANDARD, VEGETARIAN, VEGAN, etc.

### Data Quality Tips
1. **Date Format**: Use YYYY-MM-DD format
2. **Missing Values**: System handles missing data automatically
3. **Duplicates**: Automatically removed during processing
4. **Outliers**: Extreme values are flagged and handled
5. **Minimum Size**: At least 1,000 records recommended

### Sample Data Structure
```csv
booking_date,flight_date,route,ticket_price,passenger_type,booking_channel,no_show
2023-01-15,2023-02-10,NYC-LAX,299.99,ECONOMY,ONLINE,0
2023-01-16,2023-02-11,LAX-NYC,450.00,BUSINESS,AGENT,1
2023-01-17,2023-02-12,CHI-MIA,199.99,ECONOMY,PHONE,0
```

## Understanding Results

### Model Performance Metrics

#### Accuracy
- **Target**: >95%
- **Interpretation**: Percentage of correct predictions
- **Business Impact**: Higher accuracy = better overbooking decisions

#### Precision
- **Definition**: True positives / (True positives + False positives)
- **Business Impact**: Reduces false alarms (predicting no-show when passenger shows)

#### Recall
- **Definition**: True positives / (True positives + False negatives)
- **Business Impact**: Catches actual no-shows (reduces lost revenue)

#### F1-Score
- **Definition**: Harmonic mean of precision and recall
- **Business Impact**: Balanced performance metric

### Key Business Insights

#### No-Show Rate Analysis
- Overall rate vs. industry benchmarks
- Trends by passenger type, route, season
- High-risk segments identification

#### Financial Impact
- Current annual cost of no-shows
- Potential savings with improved prediction
- ROI calculation for system implementation

#### Operational Insights
- Peak no-show periods
- Route-specific patterns
- Booking channel effectiveness

### Visualization Interpretation

#### Correlation Heatmap
- Shows relationships between variables
- Darker colors = stronger correlations
- Focus on correlations with no_show variable

#### Feature Importance
- Ranks factors influencing no-shows
- Guides business strategy focus
- Identifies data collection priorities

#### Temporal Patterns
- Seasonal trends in no-shows
- Day-of-week patterns
- Lead time impact analysis

## Business Implementation

### Phase 1: Foundation (1-2 months)
**Objectives**: Set up prediction infrastructure
**Activities**:
- Deploy trained models to production
- Integrate with booking system
- Establish data pipeline
- Train initial staff

**Success Metrics**:
- Model accuracy >95% in production
- Real-time prediction latency <100ms
- Data pipeline 99.9% uptime

### Phase 2: Integration (2-3 months)
**Objectives**: Implement business processes
**Activities**:
- Dynamic overbooking strategies
- Customer communication workflows
- Staff training programs
- Monitoring dashboards

**Success Metrics**:
- 20% reduction in no-show rate
- 15% increase in load factor
- Customer satisfaction maintained >4.5/5

### Phase 3: Optimization (1-2 months)
**Objectives**: Fine-tune and scale
**Activities**:
- A/B testing of strategies
- Model retraining pipeline
- Advanced analytics
- ROI measurement

**Success Metrics**:
- Target no-show rate achieved
- Positive ROI demonstrated
- System fully automated

### Risk Mitigation

#### Technical Risks
- **Model drift**: Implement continuous monitoring
- **Data quality**: Automated validation checks
- **System downtime**: Redundancy and failover

#### Business Risks
- **Customer dissatisfaction**: Conservative overbooking limits
- **Revenue loss**: Gradual implementation
- **Staff resistance**: Comprehensive training

### Success Monitoring

#### Daily Metrics
- No-show rate vs. prediction
- Overbooking incidents
- Customer complaints

#### Weekly Metrics
- Model accuracy trends
- Financial impact measurement
- Operational efficiency

#### Monthly Metrics
- ROI calculation
- Customer satisfaction surveys
- Staff feedback assessment

## Troubleshooting

### Common Issues

#### Installation Problems
```bash
# Update pip first
pip install --upgrade pip

# Install with specific Python version
python3.8 -m pip install -r requirements.txt

# Use virtual environment
python -m venv airline_env
source airline_env/bin/activate  # Linux/Mac
airline_env\Scripts\activate     # Windows
pip install -r requirements.txt
```

#### Data Loading Errors
- **File not found**: Check file path and permissions
- **Encoding issues**: Save CSV as UTF-8
- **Date parsing**: Use YYYY-MM-DD format
- **Missing columns**: Verify required columns exist

#### Memory Issues
```bash
# For large datasets, use chunking
python main.py --data large_file.csv --config config_small_batch.yaml

# Or reduce sample size
python sample_data_generator.py --samples 5000
```

#### Model Performance Issues
- **Low accuracy**: Check data quality and size
- **Overfitting**: Reduce model complexity in config.yaml
- **Underfitting**: Increase model complexity or add features

### Getting Help

#### Log Files
Check `logs/` directory for detailed error messages:
```bash
# View latest log
ls -la logs/
cat logs/airline_noshow_YYYYMMDD_HHMMSS.log
```

#### Test System
```bash
# Run diagnostic tests
python run_tests.py

# Test with sample data
python sample_data_generator.py --samples 1000
python main.py --data sample_booking_data.csv
```

#### Performance Optimization
- Use SSD storage for better I/O performance
- Increase RAM for larger datasets
- Use GPU acceleration for XGBoost (optional)

### Support Resources
- Check README.md for latest updates
- Review config.yaml for customization options
- Use sample data to isolate issues
- Monitor system logs for detailed diagnostics
