#!/usr/bin/env python3
"""
Airline No-Show Prediction System
Main entry point for the comprehensive ML system
"""

import os
import sys
import argparse
import yaml
import pandas as pd
from pathlib import Path

# Add src directory to path
sys.path.append(str(Path(__file__).parent / "src"))

from data_processor import DataProcessor
from eda_analyzer import EDAAnalyzer
from model_trainer import ModelTrainer
from visualizer import Visualizer
from business_intelligence import BusinessIntelligence
from utils.logger import setup_logger

def load_config(config_path="config.yaml"):
    """Load configuration from YAML file"""
    with open(config_path, 'r') as file:
        return yaml.safe_load(file)

def create_directories():
    """Create necessary directories for the project"""
    directories = [
        "data/raw",
        "data/processed", 
        "models",
        "reports",
        "visualizations",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)

def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(description="Airline No-Show Prediction System")
    parser.add_argument("--data", required=True, help="Path to input CSV file")
    parser.add_argument("--config", default="config.yaml", help="Path to configuration file")
    parser.add_argument("--output", default="reports", help="Output directory for reports")
    parser.add_argument("--mode", choices=["train", "predict", "full"], default="full",
                       help="Execution mode: train, predict, or full pipeline")
    
    args = parser.parse_args()
    
    # Setup
    create_directories()
    config = load_config(args.config)
    logger = setup_logger()
    
    logger.info("Starting Airline No-Show Prediction System")
    logger.info(f"Input data: {args.data}")
    logger.info(f"Mode: {args.mode}")
    
    try:
        # Load data
        logger.info("Loading data...")
        data = pd.read_csv(args.data)
        logger.info(f"Data loaded: {data.shape[0]} rows, {data.shape[1]} columns")
        
        # Initialize components
        data_processor = DataProcessor(config)
        eda_analyzer = EDAAnalyzer(config)
        model_trainer = ModelTrainer(config)
        visualizer = Visualizer(config)
        business_intel = BusinessIntelligence(config)
        
        if args.mode in ["train", "full"]:
            # Data Processing
            logger.info("Processing data...")
            processed_data = data_processor.process(data)
            
            # Exploratory Data Analysis
            logger.info("Performing EDA...")
            eda_results = eda_analyzer.analyze(processed_data)
            
            # Create visualizations
            logger.info("Creating visualizations...")
            visualizer.create_dashboard(processed_data, eda_results)
            
            # Train models
            logger.info("Training models...")
            model_results = model_trainer.train_and_evaluate(processed_data)
            
            # Generate business insights
            logger.info("Generating business insights...")
            business_insights = business_intel.generate_insights(
                processed_data, model_results, eda_results
            )
            
            # Save results
            logger.info("Saving results...")
            business_intel.save_executive_summary(
                business_insights, f"{args.output}/executive_summary.html"
            )
            
        elif args.mode == "predict":
            # Load trained model and make predictions
            logger.info("Loading trained model for predictions...")
            # Implementation for prediction mode
            pass
            
        logger.info("Pipeline completed successfully!")
        
    except Exception as e:
        logger.error(f"Error in pipeline: {str(e)}")
        raise

if __name__ == "__main__":
    main()
