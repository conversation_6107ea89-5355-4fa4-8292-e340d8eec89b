#!/usr/bin/env python3
"""
Sample Data Generator for Airline No-Show Prediction System
Creates realistic sample booking data for testing and demonstration
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import argparse

def generate_sample_data(n_samples=10000, output_file="sample_booking_data.csv"):
    """
    Generate realistic airline booking sample data
    
    Args:
        n_samples (int): Number of booking records to generate
        output_file (str): Output CSV filename
    """
    np.random.seed(42)  # For reproducible results
    
    print(f"Generating {n_samples} sample booking records...")
    
    # Date ranges
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2023, 12, 31)
    date_range = (end_date - start_date).days
    
    # Generate booking dates
    booking_dates = [start_date + timedelta(days=np.random.randint(0, date_range)) 
                    for _ in range(n_samples)]
    
    # Generate flight dates (1-90 days after booking)
    flight_dates = [booking_date + timedelta(days=np.random.randint(1, 91)) 
                   for booking_date in booking_dates]
    
    # Route data (realistic US domestic routes)
    routes = [
        'NYC-LAX', 'LAX-NYC', 'CHI-MIA', 'MIA-CHI', 'BOS-SEA', 'SEA-BOS',
        'DEN-ATL', 'ATL-DEN', 'LAS-DFW', 'DFW-LAS', 'SFO-ORD', 'ORD-SFO',
        'PHX-JFK', 'JFK-PHX', 'MSP-LAX', 'LAX-MSP', 'DTW-MIA', 'MIA-DTW',
        'BWI-DEN', 'DEN-BWI', 'PDX-ATL', 'ATL-PDX', 'SAN-BOS', 'BOS-SAN'
    ]
    route_list = np.random.choice(routes, n_samples)
    
    # Passenger types with realistic distribution
    passenger_types = np.random.choice(
        ['ECONOMY', 'BUSINESS', 'FIRST'], 
        n_samples, 
        p=[0.75, 0.20, 0.05]
    )
    
    # Booking channels
    booking_channels = np.random.choice(
        ['ONLINE', 'AGENT', 'PHONE', 'MOBILE_APP'], 
        n_samples, 
        p=[0.50, 0.25, 0.15, 0.10]
    )
    
    # Generate ticket prices based on passenger type and route popularity
    price_base = {'ECONOMY': 250, 'BUSINESS': 750, 'FIRST': 1500}
    route_multipliers = {route: np.random.uniform(0.8, 1.5) for route in routes}
    
    ticket_prices = []
    for i in range(n_samples):
        base_price = price_base[passenger_types[i]]
        route_mult = route_multipliers[route_list[i]]
        # Add some randomness
        price = base_price * route_mult * np.random.uniform(0.7, 1.3)
        # Add seasonal variation
        month = booking_dates[i].month
        if month in [6, 7, 8, 12]:  # Peak season
            price *= 1.2
        elif month in [1, 2, 9, 10]:  # Low season
            price *= 0.8
        
        ticket_prices.append(max(50, round(price, 2)))  # Minimum $50
    
    # Calculate booking lead time
    booking_lead_times = [(flight_dates[i] - booking_dates[i]).days 
                         for i in range(n_samples)]
    
    # Generate previous no-shows (passenger history)
    previous_no_shows = np.random.poisson(0.5, n_samples)  # Average 0.5 previous no-shows
    
    # Generate meal preferences
    meal_preferences = np.random.choice(
        ['STANDARD', 'VEGETARIAN', 'VEGAN', 'KOSHER', 'HALAL', 'NONE'], 
        n_samples, 
        p=[0.60, 0.15, 0.05, 0.05, 0.05, 0.10]
    )
    
    # Calculate no-show probability based on multiple factors
    no_show_probabilities = []
    for i in range(n_samples):
        # Base probability
        prob = 0.08  # 8% base no-show rate
        
        # Passenger type factor
        if passenger_types[i] == 'ECONOMY':
            prob += 0.02
        elif passenger_types[i] == 'FIRST':
            prob -= 0.03
        
        # Lead time factor (longer lead time = higher no-show)
        prob += min(0.05, booking_lead_times[i] * 0.0005)
        
        # Previous no-shows factor
        prob += previous_no_shows[i] * 0.03
        
        # Booking channel factor
        if booking_channels[i] == 'PHONE':
            prob += 0.01
        elif booking_channels[i] == 'ONLINE':
            prob -= 0.005
        
        # Price factor (very expensive or very cheap flights)
        if ticket_prices[i] > 1000:
            prob -= 0.01  # Expensive flights, less likely to no-show
        elif ticket_prices[i] < 150:
            prob += 0.02  # Cheap flights, more likely to no-show
        
        # Weekend flight factor
        if flight_dates[i].weekday() >= 5:  # Saturday or Sunday
            prob += 0.01
        
        # Holiday season factor
        if flight_dates[i].month == 12:
            prob += 0.015
        
        # Ensure probability is between 0 and 1
        prob = max(0, min(1, prob))
        no_show_probabilities.append(prob)
    
    # Generate actual no-shows based on probabilities
    no_shows = np.random.binomial(1, no_show_probabilities)
    
    # Create DataFrame
    sample_data = pd.DataFrame({
        'booking_id': [f'BK{str(i+1).zfill(8)}' for i in range(n_samples)],
        'booking_date': booking_dates,
        'flight_date': flight_dates,
        'route': route_list,
        'ticket_price': ticket_prices,
        'passenger_type': passenger_types,
        'booking_channel': booking_channels,
        'booking_lead_time': booking_lead_times,
        'previous_no_shows': previous_no_shows,
        'meal_preference': meal_preferences,
        'no_show': no_shows
    })
    
    # Add some additional derived features
    sample_data['booking_day_of_week'] = [date.strftime('%A') for date in booking_dates]
    sample_data['flight_day_of_week'] = [date.strftime('%A') for date in flight_dates]
    sample_data['booking_month'] = [date.month for date in booking_dates]
    sample_data['flight_month'] = [date.month for date in flight_dates]
    sample_data['is_weekend_booking'] = [1 if date.weekday() >= 5 else 0 for date in booking_dates]
    sample_data['is_weekend_flight'] = [1 if date.weekday() >= 5 else 0 for date in flight_dates]
    
    # Save to CSV
    sample_data.to_csv(output_file, index=False)
    
    # Print summary statistics
    print(f"\nSample data generated and saved to '{output_file}'")
    print(f"Total records: {len(sample_data):,}")
    print(f"No-show rate: {sample_data['no_show'].mean():.2%}")
    print(f"Date range: {sample_data['booking_date'].min().strftime('%Y-%m-%d')} to {sample_data['booking_date'].max().strftime('%Y-%m-%d')}")
    print(f"Average ticket price: ${sample_data['ticket_price'].mean():.2f}")
    print(f"Unique routes: {sample_data['route'].nunique()}")
    
    print("\nPassenger type distribution:")
    print(sample_data['passenger_type'].value_counts(normalize=True).round(3))
    
    print("\nBooking channel distribution:")
    print(sample_data['booking_channel'].value_counts(normalize=True).round(3))
    
    return sample_data

def main():
    """Main function with command line arguments"""
    parser = argparse.ArgumentParser(description="Generate sample airline booking data")
    parser.add_argument("--samples", type=int, default=10000, 
                       help="Number of sample records to generate (default: 10000)")
    parser.add_argument("--output", type=str, default="sample_booking_data.csv",
                       help="Output CSV filename (default: sample_booking_data.csv)")
    
    args = parser.parse_args()
    
    # Generate sample data
    generate_sample_data(args.samples, args.output)

if __name__ == "__main__":
    main()
