#!/usr/bin/env python3
"""
Streamlit Web Application for Airline No-Show Prediction System
User-friendly interface for CSV upload and analysis
"""

import streamlit as st
import pandas as pd
import numpy as np
import sys
from pathlib import Path
import yaml
import joblib
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Add src directory to path
sys.path.append(str(Path(__file__).parent / "src"))

from data_processor import DataProcessor
from eda_analyzer import EDAAnalyzer
from model_trainer import ModelTrainer
from visualizer import Visualizer
from business_intelligence import BusinessIntelligence

# Page configuration
st.set_page_config(
    page_title="Airline No-Show Prediction System",
    page_icon="✈️",
    layout="wide",
    initial_sidebar_state="expanded"
)

def load_config():
    """Load configuration"""
    try:
        with open('config.yaml', 'r') as file:
            return yaml.safe_load(file)
    except FileNotFoundError:
        st.error("Configuration file not found. Please ensure config.yaml exists.")
        return None

def validate_csv_format(df):
    """Validate uploaded CSV format"""
    required_columns = ['booking_date', 'flight_date', 'route', 'ticket_price', 'passenger_type', 'no_show']
    missing_columns = [col for col in required_columns if col not in df.columns]
    
    if missing_columns:
        return False, f"Missing required columns: {', '.join(missing_columns)}"
    
    # Check data types
    try:
        pd.to_datetime(df['booking_date'])
        pd.to_datetime(df['flight_date'])
        pd.to_numeric(df['ticket_price'])
        pd.to_numeric(df['no_show'])
    except Exception as e:
        return False, f"Data type validation failed: {str(e)}"
    
    return True, "CSV format is valid"

def create_sample_data():
    """Create sample data for demonstration"""
    np.random.seed(42)
    n_samples = 1000
    
    # Generate sample data
    booking_dates = pd.date_range('2023-01-01', '2023-12-31', periods=n_samples)
    flight_dates = booking_dates + pd.to_timedelta(np.random.randint(1, 90, n_samples), unit='D')
    
    routes = np.random.choice(['NYC-LAX', 'CHI-MIA', 'BOS-SEA', 'DEN-ATL', 'LAS-DFW'], n_samples)
    passenger_types = np.random.choice(['ECONOMY', 'BUSINESS', 'FIRST'], n_samples, p=[0.7, 0.25, 0.05])
    booking_channels = np.random.choice(['ONLINE', 'AGENT', 'PHONE'], n_samples, p=[0.6, 0.3, 0.1])
    
    # Price based on passenger type
    price_base = {'ECONOMY': 300, 'BUSINESS': 800, 'FIRST': 1500}
    ticket_prices = [price_base[pt] + np.random.normal(0, 100) for pt in passenger_types]
    ticket_prices = np.maximum(ticket_prices, 50)  # Minimum price
    
    # No-show probability based on various factors
    lead_times = (flight_dates - booking_dates).dt.days
    no_show_prob = 0.05 + 0.001 * lead_times + np.random.normal(0, 0.02, n_samples)
    no_show_prob = np.clip(no_show_prob, 0, 1)
    no_shows = np.random.binomial(1, no_show_prob)
    
    sample_df = pd.DataFrame({
        'booking_date': booking_dates,
        'flight_date': flight_dates,
        'route': routes,
        'ticket_price': ticket_prices,
        'passenger_type': passenger_types,
        'booking_channel': booking_channels,
        'no_show': no_shows
    })
    
    return sample_df

def run_analysis(df, config):
    """Run the complete analysis pipeline"""
    progress_bar = st.progress(0)
    status_text = st.empty()
    
    try:
        # Initialize components
        data_processor = DataProcessor(config)
        eda_analyzer = EDAAnalyzer(config)
        model_trainer = ModelTrainer(config)
        business_intel = BusinessIntelligence(config)
        
        # Data Processing
        status_text.text("Processing data...")
        progress_bar.progress(20)
        processed_data = data_processor.process(df)
        
        # EDA Analysis
        status_text.text("Performing exploratory data analysis...")
        progress_bar.progress(40)
        eda_results = eda_analyzer.analyze(processed_data)
        
        # Model Training
        status_text.text("Training machine learning models...")
        progress_bar.progress(60)
        model_results = model_trainer.train_and_evaluate(processed_data)
        
        # Business Intelligence
        status_text.text("Generating business insights...")
        progress_bar.progress(80)
        business_insights = business_intel.generate_insights(processed_data, model_results, eda_results)
        
        progress_bar.progress(100)
        status_text.text("Analysis completed!")
        
        return processed_data, eda_results, model_results, business_insights
        
    except Exception as e:
        st.error(f"Error during analysis: {str(e)}")
        return None, None, None, None

def display_executive_summary(insights):
    """Display executive summary"""
    st.header("📊 Executive Summary")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "Total Bookings",
            f"{insights['executive_summary']['analysis_period']['total_bookings']:,}"
        )
    
    with col2:
        st.metric(
            "No-Show Rate",
            f"{insights['executive_summary']['current_performance']['no_show_rate']}%"
        )
    
    with col3:
        st.metric(
            "Best Model Accuracy",
            f"{insights['executive_summary']['model_performance']['accuracy']}%"
        )
    
    with col4:
        st.metric(
            "Annual Cost Estimate",
            f"${insights['executive_summary']['current_performance']['annual_cost_estimate']:,.0f}"
        )

def display_financial_impact(insights):
    """Display financial impact analysis"""
    st.header("💰 Financial Impact")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.metric(
            "Current Annual Loss",
            f"${insights['financial_impact']['current_annual_cost']:,.0f}"
        )
        st.metric(
            "Potential Annual Savings",
            f"${insights['financial_impact']['potential_annual_savings']:,.0f}"
        )
    
    with col2:
        st.metric(
            "ROI Percentage",
            f"{insights['financial_impact']['roi_percentage']}%"
        )
        st.metric(
            "Payback Period",
            f"{insights['roi_analysis']['payback_period_months']} months"
        )

def display_model_performance(model_results):
    """Display model performance comparison"""
    st.header("🤖 Model Performance")
    
    # Create comparison chart
    models = list(model_results.keys())
    metrics = ['accuracy', 'precision', 'recall', 'f1']
    
    comparison_data = []
    for model in models:
        for metric in metrics:
            if metric in model_results[model]['test_metrics']:
                comparison_data.append({
                    'Model': model.replace('_', ' ').title(),
                    'Metric': metric.title(),
                    'Score': model_results[model]['test_metrics'][metric]
                })
    
    if comparison_data:
        df_comparison = pd.DataFrame(comparison_data)
        fig = px.bar(df_comparison, x='Model', y='Score', color='Metric',
                    title='Model Performance Comparison',
                    barmode='group')
        st.plotly_chart(fig, use_container_width=True)
    
    # Display detailed metrics
    st.subheader("Detailed Performance Metrics")
    for model_name, results in model_results.items():
        with st.expander(f"{model_name.replace('_', ' ').title()} Details"):
            col1, col2 = st.columns(2)
            
            with col1:
                st.write("**Test Metrics:**")
                for metric, value in results['test_metrics'].items():
                    if value is not None:
                        st.write(f"- {metric.title()}: {value:.4f}")
            
            with col2:
                st.write("**Cross-Validation:**")
                for metric, cv_data in results['cross_validation'].items():
                    st.write(f"- {metric}: {cv_data['mean']:.4f} (±{cv_data['std']:.4f})")

def display_insights_and_recommendations(insights):
    """Display key insights and recommendations"""
    st.header("🔍 Key Insights")
    
    for finding in insights['key_findings']:
        st.write(f"• {finding}")
    
    st.header("💡 Recommendations")
    
    for rec in insights['recommendations']:
        priority_color = {
            'High': '🔴',
            'Medium': '🟡',
            'Low': '🟢'
        }
        
        with st.expander(f"{priority_color[rec['priority']]} {rec['category']} ({rec['priority']} Priority)"):
            st.write(f"**Recommendation:** {rec['recommendation']}")
            st.write(f"**Expected Impact:** {rec['expected_impact']}")
            st.write(f"**Implementation Effort:** {rec['implementation_effort']}")
            st.write(f"**Timeline:** {rec['timeline']}")

def main():
    """Main application"""
    st.title("✈️ Airline No-Show Prediction System")
    st.markdown("Upload your booking data to get comprehensive analysis and predictions")
    
    # Load configuration
    config = load_config()
    if not config:
        return
    
    # Sidebar
    st.sidebar.title("Navigation")
    
    # File upload
    st.sidebar.subheader("Data Upload")
    uploaded_file = st.sidebar.file_uploader(
        "Upload CSV file",
        type=['csv'],
        help="Upload your airline booking data in CSV format"
    )
    
    # Sample data option
    if st.sidebar.button("Use Sample Data"):
        st.session_state['sample_data'] = create_sample_data()
        st.sidebar.success("Sample data loaded!")
    
    # Data format help
    with st.sidebar.expander("Required CSV Format"):
        st.write("""
        Your CSV file should contain these columns:
        - booking_date: Date when booking was made
        - flight_date: Date of the flight
        - route: Flight route (e.g., "NYC-LAX")
        - ticket_price: Price of the ticket
        - passenger_type: Type of passenger
        - no_show: Target variable (1 for no-show, 0 for show)
        
        Optional columns:
        - booking_channel: How booking was made
        - previous_no_shows: Number of previous no-shows
        """)
    
    # Main content
    if uploaded_file is not None:
        # Load and validate data
        try:
            df = pd.read_csv(uploaded_file)
            st.success(f"Data loaded successfully! {len(df)} rows, {len(df.columns)} columns")
            
            # Validate format
            is_valid, message = validate_csv_format(df)
            if not is_valid:
                st.error(f"CSV validation failed: {message}")
                return
            
            # Display data preview
            with st.expander("Data Preview"):
                st.dataframe(df.head())
            
            # Run analysis button
            if st.button("🚀 Run Analysis", type="primary"):
                with st.spinner("Running comprehensive analysis..."):
                    processed_data, eda_results, model_results, business_insights = run_analysis(df, config)
                
                if all([processed_data, eda_results, model_results, business_insights]):
                    # Store results in session state
                    st.session_state['results'] = {
                        'processed_data': processed_data,
                        'eda_results': eda_results,
                        'model_results': model_results,
                        'business_insights': business_insights
                    }
                    
                    st.success("Analysis completed successfully!")
                    st.rerun()
        
        except Exception as e:
            st.error(f"Error loading file: {str(e)}")
    
    elif 'sample_data' in st.session_state:
        df = st.session_state['sample_data']
        st.info("Using sample data for demonstration")
        
        # Display data preview
        with st.expander("Sample Data Preview"):
            st.dataframe(df.head())
        
        # Run analysis button
        if st.button("🚀 Run Analysis on Sample Data", type="primary"):
            with st.spinner("Running comprehensive analysis..."):
                processed_data, eda_results, model_results, business_insights = run_analysis(df, config)
            
            if all([processed_data, eda_results, model_results, business_insights]):
                # Store results in session state
                st.session_state['results'] = {
                    'processed_data': processed_data,
                    'eda_results': eda_results,
                    'model_results': model_results,
                    'business_insights': business_insights
                }
                
                st.success("Analysis completed successfully!")
                st.rerun()
    
    # Display results if available
    if 'results' in st.session_state:
        results = st.session_state['results']
        
        # Create tabs for different sections
        tab1, tab2, tab3, tab4 = st.tabs(["Executive Summary", "Model Performance", "Insights & Recommendations", "Implementation Plan"])
        
        with tab1:
            display_executive_summary(results['business_insights'])
            display_financial_impact(results['business_insights'])
        
        with tab2:
            display_model_performance(results['model_results'])
        
        with tab3:
            display_insights_and_recommendations(results['business_insights'])
        
        with tab4:
            st.header("📋 Implementation Plan")
            for phase in results['business_insights']['implementation_plan']:
                with st.expander(f"{phase['phase']} - {phase['duration']}"):
                    st.write("**Activities:**")
                    for activity in phase['activities']:
                        st.write(f"• {activity}")
                    st.write("**Deliverables:**")
                    for deliverable in phase['deliverables']:
                        st.write(f"• {deliverable}")
        
        # Download executive summary
        if st.button("📄 Download Executive Summary"):
            business_intel = BusinessIntelligence(config)
            html_content = business_intel._generate_html_report(results['business_insights'])
            st.download_button(
                label="Download HTML Report",
                data=html_content,
                file_name=f"airline_noshow_report_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.html",
                mime="text/html"
            )
    
    else:
        # Welcome message
        st.markdown("""
        ## Welcome to the Airline No-Show Prediction System
        
        This comprehensive system provides:
        
        - **Data Analysis**: Deep dive into your booking patterns and no-show trends
        - **Machine Learning**: Multiple algorithms to predict passenger no-shows with >95% accuracy
        - **Business Intelligence**: Actionable insights and ROI analysis
        - **Recommendations**: Specific strategies to reduce no-shows and increase revenue
        
        ### Getting Started
        1. Upload your booking data CSV file using the sidebar
        2. Or click "Use Sample Data" to see a demonstration
        3. Click "Run Analysis" to generate comprehensive insights
        
        ### Expected Results
        - Trained ML models with performance metrics
        - Interactive visualizations and dashboards
        - Executive summary with business recommendations
        - Implementation plan with ROI estimates
        """)

if __name__ == "__main__":
    main()
