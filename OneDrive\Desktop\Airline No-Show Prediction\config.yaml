# Airline No-Show Prediction System Configuration

# Data Processing Configuration
data:
  input_path: "data/raw/"
  output_path: "data/processed/"
  test_size: 0.2
  random_state: 42
  
# Feature Engineering
features:
  categorical_columns:
    - "passenger_type"
    - "route"
    - "booking_channel"
    - "meal_preference"
  
  numerical_columns:
    - "ticket_price"
    - "days_before_flight"
    - "booking_lead_time"
    - "previous_no_shows"
  
  datetime_columns:
    - "booking_date"
    - "flight_date"

# Model Configuration
models:
  algorithms:
    - "random_forest"
    - "gradient_boosting"
    - "logistic_regression"
    - "xgboost"
  
  hyperparameters:
    random_forest:
      n_estimators: [100, 200, 300]
      max_depth: [10, 20, 30]
      min_samples_split: [2, 5, 10]
    
    gradient_boosting:
      n_estimators: [100, 200]
      learning_rate: [0.01, 0.1, 0.2]
      max_depth: [3, 5, 7]
    
    logistic_regression:
      C: [0.1, 1, 10]
      penalty: ["l1", "l2"]
    
    xgboost:
      n_estimators: [100, 200]
      learning_rate: [0.01, 0.1, 0.2]
      max_depth: [3, 5, 7]

# Evaluation Metrics
evaluation:
  target_accuracy: 0.95
  cv_folds: 5
  scoring_metrics:
    - "accuracy"
    - "precision"
    - "recall"
    - "f1"
    - "roc_auc"

# Visualization Settings
visualization:
  figure_size: [12, 8]
  color_palette: "viridis"
  save_format: "png"
  dpi: 300

# Business Intelligence
business:
  cost_per_no_show: 150  # USD
  overbooking_cost: 300  # USD
  target_no_show_rate: 0.05  # 5%
