"""
Machine Learning Model Trainer for Airline No-Show Prediction System
Implements multiple algorithms with comprehensive evaluation
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import cross_val_score, GridSearchCV, StratifiedKFold
from sklearn.metrics import (accuracy_score, precision_score, recall_score, 
                           f1_score, roc_auc_score, confusion_matrix, 
                           classification_report, roc_curve)
import xgboost as xgb
import joblib
import logging
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns

class ModelTrainer:
    """
    Comprehensive machine learning model trainer for no-show prediction
    """
    
    def __init__(self, config):
        """
        Initialize ModelTrainer with configuration
        
        Args:
            config (dict): Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.models = {}
        self.results = {}
        self.best_model = None
        self.model_dir = Path("models")
        self.model_dir.mkdir(exist_ok=True)
        
    def train_and_evaluate(self, processed_data):
        """
        Train multiple models and evaluate performance
        
        Args:
            processed_data (dict): Processed data from DataProcessor
            
        Returns:
            dict: Model results and performance metrics
        """
        self.logger.info("Starting model training and evaluation")
        
        X_train = processed_data['X_train']
        X_test = processed_data['X_test']
        y_train = processed_data['y_train']
        y_test = processed_data['y_test']
        
        # Initialize models
        self._initialize_models()
        
        # Train models
        self._train_models(X_train, y_train)
        
        # Evaluate models
        self._evaluate_models(X_train, X_test, y_train, y_test)
        
        # Select best model
        self._select_best_model()
        
        # Generate feature importance
        self._analyze_feature_importance(processed_data['feature_names'])
        
        # Create visualizations
        self.create_model_comparison_chart()
        self.create_confusion_matrices()
        
        # Save models
        self._save_models()
        
        self.logger.info("Model training and evaluation completed")
        return self.results
    
    def _initialize_models(self):
        """Initialize ML models with hyperparameters"""
        self.logger.info("Initializing models...")
        
        self.models = {
            'random_forest': RandomForestClassifier(
                n_estimators=200,
                max_depth=20,
                min_samples_split=5,
                random_state=self.config['data']['random_state'],
                n_jobs=-1
            ),
            'gradient_boosting': GradientBoostingClassifier(
                n_estimators=200,
                learning_rate=0.1,
                max_depth=5,
                random_state=self.config['data']['random_state']
            ),
            'logistic_regression': LogisticRegression(
                C=1.0,
                penalty='l2',
                random_state=self.config['data']['random_state'],
                max_iter=1000
            ),
            'xgboost': xgb.XGBClassifier(
                n_estimators=200,
                learning_rate=0.1,
                max_depth=5,
                random_state=self.config['data']['random_state'],
                eval_metric='logloss'
            )
        }
    
    def _train_models(self, X_train, y_train):
        """Train all models"""
        self.logger.info("Training models...")
        
        for name, model in self.models.items():
            self.logger.info(f"Training {name}...")
            try:
                model.fit(X_train, y_train)
                self.logger.info(f"{name} training completed")
            except Exception as e:
                self.logger.error(f"Error training {name}: {e}")
                
    def _evaluate_models(self, X_train, X_test, y_train, y_test):
        """Evaluate all trained models"""
        self.logger.info("Evaluating models...")
        
        cv = StratifiedKFold(n_splits=self.config['evaluation']['cv_folds'], 
                           shuffle=True, random_state=self.config['data']['random_state'])
        
        for name, model in self.models.items():
            self.logger.info(f"Evaluating {name}...")
            
            try:
                # Cross-validation scores
                cv_scores = {}
                for metric in self.config['evaluation']['scoring_metrics']:
                    scores = cross_val_score(model, X_train, y_train, cv=cv, scoring=metric)
                    cv_scores[f'cv_{metric}'] = {
                        'mean': scores.mean(),
                        'std': scores.std(),
                        'scores': scores.tolist()
                    }
                
                # Test set predictions
                y_pred = model.predict(X_test)
                y_pred_proba = model.predict_proba(X_test)[:, 1] if hasattr(model, 'predict_proba') else None
                
                # Calculate metrics
                test_metrics = {
                    'accuracy': accuracy_score(y_test, y_pred),
                    'precision': precision_score(y_test, y_pred),
                    'recall': recall_score(y_test, y_pred),
                    'f1': f1_score(y_test, y_pred),
                    'roc_auc': roc_auc_score(y_test, y_pred_proba) if y_pred_proba is not None else None
                }
                
                # Confusion matrix
                cm = confusion_matrix(y_test, y_pred)
                
                # Classification report
                class_report = classification_report(y_test, y_pred, output_dict=True)
                
                # Store results
                self.results[name] = {
                    'cross_validation': cv_scores,
                    'test_metrics': test_metrics,
                    'confusion_matrix': cm.tolist(),
                    'classification_report': class_report,
                    'predictions': y_pred.tolist(),
                    'prediction_probabilities': y_pred_proba.tolist() if y_pred_proba is not None else None
                }
                
                self.logger.info(f"{name} evaluation completed - Accuracy: {test_metrics['accuracy']:.4f}")
                
            except Exception as e:
                self.logger.error(f"Error evaluating {name}: {e}")
    
    def _select_best_model(self):
        """Select the best performing model"""
        self.logger.info("Selecting best model...")
        
        best_score = 0
        best_model_name = None
        
        for name, results in self.results.items():
            # Use F1 score as primary metric, with accuracy as tiebreaker
            f1_score = results['test_metrics']['f1']
            accuracy = results['test_metrics']['accuracy']
            
            # Check if meets minimum accuracy requirement
            if accuracy >= self.config['evaluation']['target_accuracy']:
                if f1_score > best_score:
                    best_score = f1_score
                    best_model_name = name
        
        if best_model_name:
            self.best_model = {
                'name': best_model_name,
                'model': self.models[best_model_name],
                'results': self.results[best_model_name]
            }
            self.logger.info(f"Best model selected: {best_model_name} (F1: {best_score:.4f})")
        else:
            # If no model meets target accuracy, select best F1 score
            best_model_name = max(self.results.keys(), 
                                key=lambda x: self.results[x]['test_metrics']['f1'])
            self.best_model = {
                'name': best_model_name,
                'model': self.models[best_model_name],
                'results': self.results[best_model_name]
            }
            self.logger.warning(f"No model met target accuracy. Selected: {best_model_name}")
    
    def _analyze_feature_importance(self, feature_names):
        """Analyze feature importance for tree-based models"""
        self.logger.info("Analyzing feature importance...")
        
        for name, model in self.models.items():
            if hasattr(model, 'feature_importances_'):
                importance_df = pd.DataFrame({
                    'feature': feature_names,
                    'importance': model.feature_importances_
                }).sort_values('importance', ascending=False)
                
                self.results[name]['feature_importance'] = importance_df.to_dict('records')
                
                # Create feature importance plot
                plt.figure(figsize=(10, 8))
                top_features = importance_df.head(15)
                plt.barh(range(len(top_features)), top_features['importance'])
                plt.yticks(range(len(top_features)), top_features['feature'])
                plt.xlabel('Feature Importance')
                plt.title(f'Top 15 Feature Importance - {name}')
                plt.gca().invert_yaxis()
                plt.tight_layout()
                plt.savefig(self.model_dir / f'{name}_feature_importance.png', 
                           dpi=300, bbox_inches='tight')
                plt.close()
    
    def _save_models(self):
        """Save trained models"""
        self.logger.info("Saving models...")
        
        for name, model in self.models.items():
            model_path = self.model_dir / f'{name}_model.joblib'
            joblib.dump(model, model_path)
            self.logger.info(f"Saved {name} to {model_path}")
        
        # Save best model separately
        if self.best_model:
            best_model_path = self.model_dir / 'best_model.joblib'
            joblib.dump(self.best_model['model'], best_model_path)
            self.logger.info(f"Saved best model ({self.best_model['name']}) to {best_model_path}")
        
        # Save results
        results_path = self.model_dir / 'model_results.joblib'
        joblib.dump(self.results, results_path)
        self.logger.info(f"Saved results to {results_path}")
    
    def create_model_comparison_chart(self):
        """Create model comparison visualization"""
        if not self.results:
            return
        
        # Prepare data for comparison
        models = list(self.results.keys())
        metrics = ['accuracy', 'precision', 'recall', 'f1', 'roc_auc']
        
        comparison_data = []
        for model in models:
            for metric in metrics:
                if metric in self.results[model]['test_metrics'] and self.results[model]['test_metrics'][metric] is not None:
                    comparison_data.append({
                        'Model': model,
                        'Metric': metric,
                        'Score': self.results[model]['test_metrics'][metric]
                    })
        
        if comparison_data:
            df = pd.DataFrame(comparison_data)
            
            plt.figure(figsize=(12, 8))
            sns.barplot(data=df, x='Model', y='Score', hue='Metric')
            plt.title('Model Performance Comparison')
            plt.ylabel('Score')
            plt.xticks(rotation=45)
            plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
            plt.tight_layout()
            plt.savefig(self.model_dir / 'model_comparison.png', dpi=300, bbox_inches='tight')
            plt.close()
    
    def create_confusion_matrices(self):
        """Create confusion matrix visualizations for all models"""
        if not self.results:
            return
        
        n_models = len(self.results)
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        axes = axes.ravel()
        
        for i, (name, results) in enumerate(self.results.items()):
            if i < len(axes):
                cm = np.array(results['confusion_matrix'])
                sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=axes[i])
                axes[i].set_title(f'{name} - Confusion Matrix')
                axes[i].set_xlabel('Predicted')
                axes[i].set_ylabel('Actual')
        
        # Hide unused subplots
        for i in range(len(self.results), len(axes)):
            axes[i].set_visible(False)
        
        plt.tight_layout()
        plt.savefig(self.model_dir / 'confusion_matrices.png', dpi=300, bbox_inches='tight')
        plt.close()
