"""
Business Intelligence Module for Airline No-Show Prediction System
Generates actionable insights and recommendations
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime
from pathlib import Path
import json

class BusinessIntelligence:
    """
    Business intelligence and insights generator for airline no-show analysis
    """
    
    def __init__(self, config):
        """
        Initialize Business Intelligence module
        
        Args:
            config (dict): Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.cost_per_no_show = config['business']['cost_per_no_show']
        self.overbooking_cost = config['business']['overbooking_cost']
        self.target_no_show_rate = config['business']['target_no_show_rate']
        
    def generate_insights(self, processed_data, model_results, eda_results):
        """
        Generate comprehensive business insights
        
        Args:
            processed_data (dict): Processed data from DataProcessor
            model_results (dict): Results from ModelTrainer
            eda_results (dict): Results from EDA analysis
            
        Returns:
            dict: Business insights and recommendations
        """
        self.logger.info("Generating business insights...")
        
        data = processed_data['processed_data']
        
        insights = {
            'executive_summary': self._generate_executive_summary(data, model_results, eda_results),
            'financial_impact': self._calculate_financial_impact(data, model_results),
            'key_findings': self._extract_key_findings(eda_results),
            'recommendations': self._generate_recommendations(data, eda_results, model_results),
            'roi_analysis': self._calculate_roi_analysis(data, model_results),
            'risk_assessment': self._assess_risks(data, eda_results),
            'implementation_plan': self._create_implementation_plan(),
            'monitoring_metrics': self._define_monitoring_metrics()
        }
        
        self.logger.info("Business insights generated successfully")
        return insights
    
    def _generate_executive_summary(self, data, model_results, eda_results):
        """Generate executive summary"""
        total_bookings = len(data)
        total_no_shows = data['no_show'].sum()
        current_no_show_rate = data['no_show'].mean()
        
        # Get best model performance
        best_model_name = None
        best_accuracy = 0
        for name, results in model_results.items():
            if results['test_metrics']['accuracy'] > best_accuracy:
                best_accuracy = results['test_metrics']['accuracy']
                best_model_name = name
        
        summary = {
            'analysis_period': {
                'start_date': data['booking_date'].min().strftime('%Y-%m-%d') if 'booking_date' in data.columns else 'N/A',
                'end_date': data['booking_date'].max().strftime('%Y-%m-%d') if 'booking_date' in data.columns else 'N/A',
                'total_bookings': int(total_bookings),
                'total_no_shows': int(total_no_shows)
            },
            'current_performance': {
                'no_show_rate': round(current_no_show_rate * 100, 2),
                'annual_cost_estimate': round(total_no_shows * self.cost_per_no_show * 12, 2),
                'target_no_show_rate': round(self.target_no_show_rate * 100, 2)
            },
            'model_performance': {
                'best_model': best_model_name,
                'accuracy': round(best_accuracy * 100, 2),
                'meets_target': best_accuracy >= self.config['evaluation']['target_accuracy']
            },
            'potential_savings': self._calculate_potential_savings(data, model_results)
        }
        
        return summary
    
    def _calculate_financial_impact(self, data, model_results):
        """Calculate financial impact of no-shows and potential improvements"""
        current_no_shows = data['no_show'].sum()
        current_cost = current_no_shows * self.cost_per_no_show
        
        # Calculate potential reduction with best model
        best_model_results = None
        best_accuracy = 0
        for results in model_results.values():
            if results['test_metrics']['accuracy'] > best_accuracy:
                best_accuracy = results['test_metrics']['accuracy']
                best_model_results = results
        
        if best_model_results:
            # Estimate reduction in no-shows with improved prediction
            predicted_reduction = 0.3  # Assume 30% reduction with better prediction
            potential_no_show_reduction = current_no_shows * predicted_reduction
            potential_savings = potential_no_show_reduction * self.cost_per_no_show
        else:
            potential_savings = 0
        
        return {
            'current_annual_cost': round(current_cost * 12, 2),
            'potential_annual_savings': round(potential_savings * 12, 2),
            'roi_percentage': round((potential_savings * 12) / (current_cost * 12) * 100, 2) if current_cost > 0 else 0,
            'break_even_months': 6  # Estimated implementation time
        }
    
    def _extract_key_findings(self, eda_results):
        """Extract key findings from EDA results"""
        findings = []
        
        # No-show rate findings
        if 'no_show_analysis' in eda_results:
            overall_rate = eda_results['no_show_analysis']['overall_rate']
            findings.append(f"Overall no-show rate is {overall_rate:.1%}")
            
            # Passenger type insights
            if 'by_passenger_type' in eda_results['no_show_analysis']:
                passenger_analysis = eda_results['no_show_analysis']['by_passenger_type']
                highest_rate_type = passenger_analysis['mean'].idxmax()
                highest_rate = passenger_analysis['mean'].max()
                findings.append(f"{highest_rate_type} passengers have the highest no-show rate at {highest_rate:.1%}")
        
        # Temporal patterns
        if 'temporal_patterns' in eda_results:
            findings.append("Temporal patterns identified in booking and flight timing")
            
            # Weekend analysis
            if 'weekend_flight' in eda_results['temporal_patterns']:
                weekend_analysis = eda_results['temporal_patterns']['weekend_flight']
                if len(weekend_analysis) > 1:
                    weekend_rate = weekend_analysis.loc[1, 'mean'] if 1 in weekend_analysis.index else 0
                    weekday_rate = weekend_analysis.loc[0, 'mean'] if 0 in weekend_analysis.index else 0
                    if weekend_rate > weekday_rate:
                        findings.append(f"Weekend flights have {weekend_rate:.1%} no-show rate vs {weekday_rate:.1%} for weekdays")
        
        # Route insights
        if 'route_analysis' in eda_results and 'top_routes_by_no_show_rate' in eda_results['route_analysis']:
            top_problem_route = eda_results['route_analysis']['top_routes_by_no_show_rate'].index[0]
            top_problem_rate = eda_results['route_analysis']['top_routes_by_no_show_rate'].iloc[0]
            findings.append(f"Route {top_problem_route} has the highest no-show rate at {top_problem_rate:.1%}")
        
        # Price correlation
        if 'correlations' in eda_results and 'no_show_correlations' in eda_results['correlations']:
            correlations = eda_results['correlations']['no_show_correlations']
            if 'ticket_price' in correlations.index:
                price_corr = correlations['ticket_price']
                if abs(price_corr) > 0.1:
                    direction = "positively" if price_corr > 0 else "negatively"
                    findings.append(f"Ticket price is {direction} correlated with no-shows (r={price_corr:.3f})")
        
        return findings
    
    def _generate_recommendations(self, data, eda_results, model_results):
        """Generate actionable recommendations"""
        recommendations = []
        
        # Model deployment recommendation
        best_model = max(model_results.keys(), 
                        key=lambda x: model_results[x]['test_metrics']['accuracy'])
        best_accuracy = model_results[best_model]['test_metrics']['accuracy']
        
        recommendations.append({
            'category': 'Model Deployment',
            'priority': 'High',
            'recommendation': f"Deploy {best_model} model with {best_accuracy:.1%} accuracy for real-time no-show prediction",
            'expected_impact': 'High',
            'implementation_effort': 'Medium',
            'timeline': '2-3 months'
        })
        
        # Overbooking strategy
        current_no_show_rate = data['no_show'].mean()
        if current_no_show_rate > self.target_no_show_rate:
            recommendations.append({
                'category': 'Overbooking Strategy',
                'priority': 'High',
                'recommendation': f"Implement dynamic overbooking based on predicted no-show rates (current: {current_no_show_rate:.1%})",
                'expected_impact': 'High',
                'implementation_effort': 'High',
                'timeline': '3-6 months'
            })
        
        # Route-specific strategies
        if 'route_analysis' in eda_results:
            recommendations.append({
                'category': 'Route Management',
                'priority': 'Medium',
                'recommendation': "Implement route-specific no-show mitigation strategies for high-risk routes",
                'expected_impact': 'Medium',
                'implementation_effort': 'Medium',
                'timeline': '1-2 months'
            })
        
        # Passenger engagement
        recommendations.append({
            'category': 'Customer Engagement',
            'priority': 'Medium',
            'recommendation': "Implement automated reminder system for high-risk passengers 24-48 hours before flight",
            'expected_impact': 'Medium',
            'implementation_effort': 'Low',
            'timeline': '1 month'
        })
        
        # Pricing strategy
        if 'price_analysis' in eda_results:
            recommendations.append({
                'category': 'Pricing Strategy',
                'priority': 'Low',
                'recommendation': "Consider dynamic pricing based on no-show risk assessment",
                'expected_impact': 'Medium',
                'implementation_effort': 'High',
                'timeline': '6-12 months'
            })
        
        return recommendations
    
    def _calculate_roi_analysis(self, data, model_results):
        """Calculate return on investment analysis"""
        # Implementation costs (estimated)
        implementation_costs = {
            'model_development': 50000,
            'system_integration': 75000,
            'training': 25000,
            'maintenance_annual': 30000
        }
        
        total_implementation_cost = sum(implementation_costs.values()) - implementation_costs['maintenance_annual']
        
        # Current losses
        annual_no_shows = data['no_show'].sum() * 12  # Extrapolate to annual
        current_annual_loss = annual_no_shows * self.cost_per_no_show
        
        # Projected savings (conservative estimate)
        projected_reduction = 0.25  # 25% reduction in no-shows
        annual_savings = current_annual_loss * projected_reduction
        
        # ROI calculation
        net_annual_benefit = annual_savings - implementation_costs['maintenance_annual']
        roi_percentage = (net_annual_benefit / total_implementation_cost) * 100
        payback_period = total_implementation_cost / net_annual_benefit if net_annual_benefit > 0 else float('inf')
        
        return {
            'implementation_cost': total_implementation_cost,
            'annual_maintenance_cost': implementation_costs['maintenance_annual'],
            'current_annual_loss': round(current_annual_loss, 2),
            'projected_annual_savings': round(annual_savings, 2),
            'net_annual_benefit': round(net_annual_benefit, 2),
            'roi_percentage': round(roi_percentage, 2),
            'payback_period_months': round(payback_period * 12, 1) if payback_period != float('inf') else 'N/A',
            'five_year_npv': round(net_annual_benefit * 5 - total_implementation_cost, 2)
        }
    
    def _assess_risks(self, data, eda_results):
        """Assess implementation and business risks"""
        risks = [
            {
                'category': 'Technical Risk',
                'risk': 'Model performance degradation over time',
                'probability': 'Medium',
                'impact': 'High',
                'mitigation': 'Implement continuous model monitoring and retraining pipeline'
            },
            {
                'category': 'Business Risk',
                'risk': 'Customer dissatisfaction from overbooking',
                'probability': 'Medium',
                'impact': 'High',
                'mitigation': 'Conservative overbooking limits and robust compensation policies'
            },
            {
                'category': 'Data Risk',
                'risk': 'Data quality issues affecting predictions',
                'probability': 'Low',
                'impact': 'High',
                'mitigation': 'Implement data validation and quality monitoring systems'
            },
            {
                'category': 'Operational Risk',
                'risk': 'Staff resistance to new prediction system',
                'probability': 'Medium',
                'impact': 'Medium',
                'mitigation': 'Comprehensive training and change management program'
            }
        ]
        
        return risks
    
    def _create_implementation_plan(self):
        """Create detailed implementation plan"""
        phases = [
            {
                'phase': 'Phase 1: Foundation',
                'duration': '1-2 months',
                'activities': [
                    'Set up data infrastructure',
                    'Implement model training pipeline',
                    'Develop basic prediction API'
                ],
                'deliverables': ['Trained models', 'API endpoints', 'Data pipeline']
            },
            {
                'phase': 'Phase 2: Integration',
                'duration': '2-3 months',
                'activities': [
                    'Integrate with booking system',
                    'Develop user interface',
                    'Implement monitoring dashboard'
                ],
                'deliverables': ['Integrated system', 'User interface', 'Monitoring tools']
            },
            {
                'phase': 'Phase 3: Optimization',
                'duration': '1-2 months',
                'activities': [
                    'Fine-tune overbooking strategies',
                    'Implement automated actions',
                    'Staff training and rollout'
                ],
                'deliverables': ['Optimized system', 'Trained staff', 'Full deployment']
            }
        ]
        
        return phases
    
    def _define_monitoring_metrics(self):
        """Define key metrics for ongoing monitoring"""
        metrics = [
            {
                'metric': 'Model Accuracy',
                'target': '>95%',
                'frequency': 'Weekly',
                'alert_threshold': '<90%'
            },
            {
                'metric': 'No-Show Rate',
                'target': f'<{self.target_no_show_rate:.1%}',
                'frequency': 'Daily',
                'alert_threshold': f'>{self.target_no_show_rate * 1.2:.1%}'
            },
            {
                'metric': 'Overbooking Rate',
                'target': '<2%',
                'frequency': 'Daily',
                'alert_threshold': '>5%'
            },
            {
                'metric': 'Customer Satisfaction',
                'target': '>4.5/5',
                'frequency': 'Monthly',
                'alert_threshold': '<4.0/5'
            },
            {
                'metric': 'Cost Savings',
                'target': f'>${self._calculate_potential_savings(None, None):,.0f}/month',
                'frequency': 'Monthly',
                'alert_threshold': 'Below 80% of target'
            }
        ]
        
        return metrics
    
    def _calculate_potential_savings(self, data, model_results):
        """Calculate potential monthly savings"""
        if data is None:
            return 50000  # Default estimate
        
        current_monthly_no_shows = data['no_show'].sum()
        estimated_reduction = 0.25  # 25% reduction
        monthly_savings = current_monthly_no_shows * estimated_reduction * self.cost_per_no_show
        
        return monthly_savings
    
    def save_executive_summary(self, insights, output_path):
        """Save executive summary as HTML report"""
        html_content = self._generate_html_report(insights)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        self.logger.info(f"Executive summary saved to {output_path}")
    
    def _generate_html_report(self, insights):
        """Generate HTML report from insights"""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Airline No-Show Prediction - Executive Summary</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                .header {{ background-color: #2c3e50; color: white; padding: 20px; text-align: center; }}
                .section {{ margin: 20px 0; padding: 15px; border-left: 4px solid #3498db; }}
                .metric {{ background-color: #ecf0f1; padding: 10px; margin: 5px 0; }}
                .recommendation {{ background-color: #e8f5e8; padding: 10px; margin: 5px 0; border-radius: 5px; }}
                .high-priority {{ border-left: 4px solid #e74c3c; }}
                .medium-priority {{ border-left: 4px solid #f39c12; }}
                .low-priority {{ border-left: 4px solid #27ae60; }}
                table {{ width: 100%; border-collapse: collapse; margin: 10px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Airline No-Show Prediction System</h1>
                <h2>Executive Summary & Business Intelligence Report</h2>
                <p>Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
            
            <div class="section">
                <h2>Executive Summary</h2>
                <div class="metric">
                    <strong>Analysis Period:</strong> {insights['executive_summary']['analysis_period']['start_date']} to {insights['executive_summary']['analysis_period']['end_date']}
                </div>
                <div class="metric">
                    <strong>Total Bookings Analyzed:</strong> {insights['executive_summary']['analysis_period']['total_bookings']:,}
                </div>
                <div class="metric">
                    <strong>Current No-Show Rate:</strong> {insights['executive_summary']['current_performance']['no_show_rate']}%
                </div>
                <div class="metric">
                    <strong>Best Model Accuracy:</strong> {insights['executive_summary']['model_performance']['accuracy']}%
                </div>
                <div class="metric">
                    <strong>Estimated Annual Cost:</strong> ${insights['executive_summary']['current_performance']['annual_cost_estimate']:,.2f}
                </div>
            </div>
            
            <div class="section">
                <h2>Financial Impact</h2>
                <div class="metric">
                    <strong>Current Annual Loss:</strong> ${insights['financial_impact']['current_annual_cost']:,.2f}
                </div>
                <div class="metric">
                    <strong>Potential Annual Savings:</strong> ${insights['financial_impact']['potential_annual_savings']:,.2f}
                </div>
                <div class="metric">
                    <strong>ROI:</strong> {insights['financial_impact']['roi_percentage']}%
                </div>
            </div>
            
            <div class="section">
                <h2>Key Findings</h2>
                <ul>
        """
        
        for finding in insights['key_findings']:
            html += f"<li>{finding}</li>"
        
        html += """
                </ul>
            </div>
            
            <div class="section">
                <h2>Recommendations</h2>
        """
        
        for rec in insights['recommendations']:
            priority_class = f"{rec['priority'].lower()}-priority"
            html += f"""
                <div class="recommendation {priority_class}">
                    <strong>{rec['category']} ({rec['priority']} Priority)</strong><br>
                    {rec['recommendation']}<br>
                    <small>Expected Impact: {rec['expected_impact']} | Timeline: {rec['timeline']}</small>
                </div>
            """
        
        html += """
            </div>
            
            <div class="section">
                <h2>Implementation Plan</h2>
                <table>
                    <tr><th>Phase</th><th>Duration</th><th>Key Activities</th><th>Deliverables</th></tr>
        """
        
        for phase in insights['implementation_plan']:
            activities = ', '.join(phase['activities'])
            deliverables = ', '.join(phase['deliverables'])
            html += f"""
                <tr>
                    <td>{phase['phase']}</td>
                    <td>{phase['duration']}</td>
                    <td>{activities}</td>
                    <td>{deliverables}</td>
                </tr>
            """
        
        html += """
                </table>
            </div>
            
            <div class="section">
                <h2>Next Steps</h2>
                <ol>
                    <li>Approve implementation budget and timeline</li>
                    <li>Assemble cross-functional implementation team</li>
                    <li>Begin Phase 1 development activities</li>
                    <li>Establish monitoring and governance framework</li>
                </ol>
            </div>
        </body>
        </html>
        """
        
        return html
