"""
Data Processing Pipeline for Airline No-Show Prediction System
Handles data cleaning, preprocessing, and feature engineering
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from sklearn.preprocessing import StandardScaler, LabelEncoder, OneHotEncoder
from sklearn.model_selection import train_test_split
import logging

class DataProcessor:
    """
    Comprehensive data processing pipeline for airline booking data
    """
    
    def __init__(self, config):
        """
        Initialize DataProcessor with configuration
        
        Args:
            config (dict): Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.scalers = {}
        self.encoders = {}
        
    def process(self, data):
        """
        Main processing pipeline
        
        Args:
            data (pd.DataFrame): Raw booking data
            
        Returns:
            dict: Processed data with train/test splits
        """
        self.logger.info("Starting data processing pipeline")
        
        # Data validation
        data = self._validate_data(data)
        
        # Data cleaning
        data = self._clean_data(data)
        
        # Feature engineering
        data = self._engineer_features(data)
        
        # Handle missing values
        data = self._handle_missing_values(data)
        
        # Encode categorical variables
        data = self._encode_categorical(data)
        
        # Scale numerical features
        data = self._scale_features(data)
        
        # Split data
        train_test_data = self._split_data(data)
        
        self.logger.info("Data processing completed successfully")
        return train_test_data
    
    def _validate_data(self, data):
        """Validate input data format and required columns"""
        required_columns = [
            'booking_date', 'flight_date', 'route', 'ticket_price',
            'passenger_type', 'no_show'
        ]
        
        missing_columns = [col for col in required_columns if col not in data.columns]
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")
        
        self.logger.info(f"Data validation passed. Shape: {data.shape}")
        return data.copy()
    
    def _clean_data(self, data):
        """Clean and standardize data"""
        self.logger.info("Cleaning data...")
        
        # Remove duplicates
        initial_rows = len(data)
        data = data.drop_duplicates()
        self.logger.info(f"Removed {initial_rows - len(data)} duplicate rows")
        
        # Convert date columns
        date_columns = ['booking_date', 'flight_date']
        for col in date_columns:
            if col in data.columns:
                data[col] = pd.to_datetime(data[col], errors='coerce')
        
        # Remove rows with invalid dates
        data = data.dropna(subset=date_columns)
        
        # Clean price data
        if 'ticket_price' in data.columns:
            data['ticket_price'] = pd.to_numeric(data['ticket_price'], errors='coerce')
            data = data[data['ticket_price'] > 0]  # Remove invalid prices
        
        # Standardize categorical data
        categorical_columns = ['route', 'passenger_type', 'booking_channel']
        for col in categorical_columns:
            if col in data.columns:
                data[col] = data[col].astype(str).str.upper().str.strip()
        
        self.logger.info(f"Data cleaning completed. Final shape: {data.shape}")
        return data
    
    def _engineer_features(self, data):
        """Create new features from existing data"""
        self.logger.info("Engineering features...")
        
        # Time-based features
        if 'booking_date' in data.columns and 'flight_date' in data.columns:
            data['booking_lead_time'] = (data['flight_date'] - data['booking_date']).dt.days
            data['booking_day_of_week'] = data['booking_date'].dt.dayofweek
            data['booking_month'] = data['booking_date'].dt.month
            data['flight_day_of_week'] = data['flight_date'].dt.dayofweek
            data['flight_month'] = data['flight_date'].dt.month
            data['is_weekend_booking'] = data['booking_day_of_week'].isin([5, 6]).astype(int)
            data['is_weekend_flight'] = data['flight_day_of_week'].isin([5, 6]).astype(int)
        
        # Price-based features
        if 'ticket_price' in data.columns:
            data['price_category'] = pd.cut(data['ticket_price'], 
                                          bins=5, labels=['Very Low', 'Low', 'Medium', 'High', 'Very High'])
            data['log_price'] = np.log1p(data['ticket_price'])
        
        # Route-based features
        if 'route' in data.columns:
            data['route_popularity'] = data.groupby('route')['route'].transform('count')
            data['route_no_show_rate'] = data.groupby('route')['no_show'].transform('mean')
        
        # Passenger history features (if available)
        if 'previous_no_shows' not in data.columns:
            data['previous_no_shows'] = 0  # Default value
        
        # Create passenger risk score
        data['passenger_risk_score'] = (
            data['previous_no_shows'] * 0.4 +
            data['booking_lead_time'] * 0.001 +
            data['is_weekend_flight'] * 0.1
        )
        
        self.logger.info(f"Feature engineering completed. New shape: {data.shape}")
        return data
    
    def _handle_missing_values(self, data):
        """Handle missing values using appropriate strategies"""
        self.logger.info("Handling missing values...")
        
        # Numerical columns - fill with median
        numerical_cols = data.select_dtypes(include=[np.number]).columns
        for col in numerical_cols:
            if data[col].isnull().sum() > 0:
                median_val = data[col].median()
                data[col].fillna(median_val, inplace=True)
                self.logger.info(f"Filled {col} missing values with median: {median_val}")
        
        # Categorical columns - fill with mode
        categorical_cols = data.select_dtypes(include=['object', 'category']).columns
        for col in categorical_cols:
            if data[col].isnull().sum() > 0:
                mode_val = data[col].mode()[0] if not data[col].mode().empty else 'UNKNOWN'
                data[col].fillna(mode_val, inplace=True)
                self.logger.info(f"Filled {col} missing values with mode: {mode_val}")
        
        return data
    
    def _encode_categorical(self, data):
        """Encode categorical variables"""
        self.logger.info("Encoding categorical variables...")
        
        categorical_columns = self.config['features']['categorical_columns']
        
        for col in categorical_columns:
            if col in data.columns:
                # Use LabelEncoder for binary categories, OneHot for multi-class
                unique_values = data[col].nunique()
                
                if unique_values == 2:
                    # Binary encoding
                    le = LabelEncoder()
                    data[f'{col}_encoded'] = le.fit_transform(data[col])
                    self.encoders[col] = le
                else:
                    # One-hot encoding
                    dummies = pd.get_dummies(data[col], prefix=col)
                    data = pd.concat([data, dummies], axis=1)
                    data.drop(col, axis=1, inplace=True)
        
        return data
    
    def _scale_features(self, data):
        """Scale numerical features"""
        self.logger.info("Scaling numerical features...")
        
        numerical_columns = self.config['features']['numerical_columns']
        
        for col in numerical_columns:
            if col in data.columns:
                scaler = StandardScaler()
                data[f'{col}_scaled'] = scaler.fit_transform(data[[col]])
                self.scalers[col] = scaler
        
        return data
    
    def _split_data(self, data):
        """Split data into training and testing sets"""
        self.logger.info("Splitting data...")
        
        # Separate features and target
        target_col = 'no_show'
        feature_cols = [col for col in data.columns if col != target_col]
        
        X = data[feature_cols]
        y = data[target_col]
        
        # Split data
        test_size = self.config['data']['test_size']
        random_state = self.config['data']['random_state']
        
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=random_state, stratify=y
        )
        
        self.logger.info(f"Data split completed:")
        self.logger.info(f"Training set: {X_train.shape[0]} samples")
        self.logger.info(f"Testing set: {X_test.shape[0]} samples")
        
        return {
            'X_train': X_train,
            'X_test': X_test,
            'y_train': y_train,
            'y_test': y_test,
            'feature_names': feature_cols,
            'processed_data': data
        }
