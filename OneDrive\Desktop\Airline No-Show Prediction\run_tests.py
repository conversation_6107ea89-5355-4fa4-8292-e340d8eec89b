#!/usr/bin/env python3
"""
Test runner for Airline No-Show Prediction System
"""

import pytest
import sys
from pathlib import Path

def run_tests():
    """Run all tests"""
    test_dir = Path(__file__).parent / "tests"
    
    # Run pytest with verbose output
    exit_code = pytest.main([
        str(test_dir),
        "-v",
        "--tb=short",
        "--color=yes"
    ])
    
    return exit_code

if __name__ == "__main__":
    exit_code = run_tests()
    sys.exit(exit_code)
