# Airline No-Show Prediction System

A comprehensive machine learning system that predicts and analyzes passenger no-shows for airlines, providing actionable business insights and recommendations with >95% accuracy target.

## 🚀 Features

- **Data Processing Pipeline**: Comprehensive data cleaning, preprocessing, and feature engineering
- **Exploratory Data Analysis**: Statistical analysis, correlation studies, and temporal pattern analysis
- **Machine Learning Models**: Multiple algorithms (Random Forest, XGBoost, Gradient Boosting, Logistic Regression)
- **Interactive Web Interface**: Streamlit-based dashboard for easy CSV upload and analysis
- **Business Intelligence**: Actionable insights, recommendations, and ROI estimates
- **Executive Reporting**: Automated HTML reports with comprehensive business insights

## 📋 Requirements

- Python 3.8+
- See `requirements.txt` for detailed dependencies

## 🛠️ Installation

1. Clone or download this repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## 🎯 Quick Start

### Option 1: Web Interface (Recommended)
```bash
streamlit run app.py
```
Then open your browser to `http://localhost:8501` and upload your CSV file or use sample data.

### Option 2: Command Line Interface
```bash
python main.py --data your_booking_data.csv
```

### Option 3: Generate Sample Data First
```bash
python sample_data_generator.py --samples 10000 --output sample_data.csv
python main.py --data sample_data.csv
```

## 📊 Usage Options

### Web Interface
- Upload CSV file through the browser interface
- Use sample data for demonstration
- Interactive visualizations and real-time analysis
- Download executive summary reports

### Command Line
```bash
# Basic usage
python main.py --data your_booking_data.csv

# Advanced usage with custom configuration
python main.py --data data.csv --config config.yaml --output reports --mode full

# Training mode only
python main.py --data data.csv --mode train

# Prediction mode (requires pre-trained models)
python main.py --data new_data.csv --mode predict
```

### Parameters
- `--data`: Path to input CSV file (required)
- `--config`: Path to configuration file (default: config.yaml)
- `--output`: Output directory for reports (default: reports)
- `--mode`: Execution mode - train, predict, or full (default: full)

## 📁 Data Format

Your CSV file should contain the following columns:

### Required Columns
- `booking_date`: Date when booking was made (YYYY-MM-DD format)
- `flight_date`: Date of the flight (YYYY-MM-DD format)
- `route`: Flight route (e.g., "NYC-LAX")
- `ticket_price`: Price of the ticket (numeric)
- `passenger_type`: Type of passenger (e.g., "ECONOMY", "BUSINESS", "FIRST")
- `no_show`: Target variable (1 for no-show, 0 for show)

### Optional Columns (will be generated if missing)
- `booking_channel`: How booking was made (e.g., "ONLINE", "AGENT", "PHONE")
- `previous_no_shows`: Number of previous no-shows by passenger
- `meal_preference`: Passenger meal preference

### Sample Data Generation
```bash
# Generate 10,000 sample records
python sample_data_generator.py --samples 10000 --output sample_data.csv

# Generate 50,000 records for larger dataset
python sample_data_generator.py --samples 50000 --output large_sample.csv
```

## 📈 Output & Results

The system generates:

### Models & Performance
- **Trained ML models** (saved in `models/` directory)
- **Model comparison charts** with accuracy, precision, recall, F1-score
- **Feature importance analysis** for business insights
- **Confusion matrices** for detailed performance evaluation

### Visualizations
- **Interactive dashboards** (`visualizations/` directory)
- **Temporal pattern analysis** (seasonal trends, day-of-week patterns)
- **Route-specific insights** (high-risk routes, pricing analysis)
- **Passenger behavior analysis** (risk scoring, booking patterns)

### Business Intelligence
- **Executive summary** with key findings and recommendations
- **ROI analysis** with implementation costs and projected savings
- **Risk assessment** with mitigation strategies
- **Implementation plan** with detailed phases and timelines

### Performance Metrics
- **Target accuracy**: >95%
- **Comprehensive evaluation**: Precision, recall, F1-score, ROC-AUC
- **Cross-validation**: 5-fold stratified validation
- **Business metrics**: Cost savings, no-show reduction estimates

## 🧪 Testing

Run the test suite to ensure everything works correctly:

```bash
# Run all tests
python run_tests.py

# Run specific test file
pytest tests/test_data_processor.py -v

# Run tests with coverage
pytest tests/ --cov=src --cov-report=html
```

## 🏗️ Project Structure

```
airline-noshow-prediction/
├── src/                           # Source code modules
│   ├── data_processor.py         # Data cleaning & preprocessing
│   ├── eda_analyzer.py           # Exploratory data analysis
│   ├── model_trainer.py          # ML model training & evaluation
│   ├── visualizer.py             # Visualization & dashboards
│   ├── business_intelligence.py  # Business insights & recommendations
│   └── utils/                    # Utility functions
├── tests/                        # Test cases
├── data/                         # Data directories (raw & processed)
├── models/                       # Trained models & results
├── reports/                      # Executive summaries & reports
├── visualizations/               # Charts, dashboards & plots
├── logs/                         # System logs
├── app.py                        # Streamlit web interface
├── main.py                       # Command-line interface
├── config.yaml                   # System configuration
├── requirements.txt              # Python dependencies
├── sample_data_generator.py      # Generate sample data
└── run_tests.py                  # Test runner
```

## 🔧 Configuration

Customize the system behavior by editing `config.yaml`:

- **Model parameters**: Algorithm selection, hyperparameters
- **Data processing**: Feature engineering, validation rules
- **Business rules**: Cost parameters, target metrics
- **Visualization**: Chart styles, color schemes

## 📞 Support & Troubleshooting

### Common Issues
1. **Import errors**: Ensure all dependencies are installed via `pip install -r requirements.txt`
2. **Data format errors**: Check CSV format matches required columns
3. **Memory issues**: Reduce sample size or use data chunking for large datasets
4. **Model performance**: Adjust hyperparameters in `config.yaml`

### Getting Help
- Check the logs in the `logs/` directory for detailed error messages
- Run tests to verify installation: `python run_tests.py`
- Use sample data to test functionality: `python sample_data_generator.py`

## 📄 License

This project is available for educational and commercial use. See implementation guidelines in the executive summary for deployment recommendations.
